<?xml version="1.0"?>

<!DOCTYPE overlay [
<!ENTITY % pref-tabmixDTD SYSTEM "chrome://tabmixplus/locale/pref-tabmix.dtd">
%pref-tabmixDTD;
]>

<overlay id="SessionPaneOverlay"
         xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
         xmlns:html="http://www.w3.org/1999/xhtml">

  <prefpane id="paneSession" onpaneload="gSessionPane.init();">

    <!-- scripts -->
    <script type="application/javascript" src="chrome://tabmixplus/content/preferences/session.js"/>

    <!-- preferences - list all preferences in this pane -->
    <preferences>
      <preference id="pref_session"             name="extensions.tabmix.session.selectedTabIndex"
                  type="int"/>
      <preference id="pref_browser.warnOnQuit"
                  name="browser.warnOnQuit"                                  type="bool"/>
      <preference id="pref_browserStartupPage"
                  name="browser.startup.page"                                type="int"/>
      <preference id="pref_resume_from_crash"
                  name="browser.sessionstore.resume_from_crash"              type="bool"/>
      <preference id="pref_ss_timeinterval"
                  name="browser.sessionstore.interval"                       type="int"/>
      <preference id="pref_ss_postdata"
                  name="browser.sessionstore.privacy_level"                  type="int"/>
    </preferences>

    <!-- pane content -->
    <tabbox id="paneSession-tabbox" selectedIndex="1">
      <tabs class="tabs-hidden">
        <tab id="sessionStore_tab" label="tab" helpTopic="tabmix"/>
      </tabs>
      <tabpanels>
        <tabpanel id="sessionsPanel">
          <vbox class="work-in-progress">
            <label class="bold-label" value="Tabmix Session Manager is not ready yet, use built-in Session Manager" />
          </vbox>
          <html:fieldset align="start" flex="1">
            <separator class="thin"/>
            <vbox style="display: inline-block;">
              <label value="&sm.start;" control="browserStartupPage" class="firefox"/>
              <menulist id="browserStartupPage" class="indent firefox" preference="pref_browserStartupPage">
                <menupopup>
                  <menuitem label="&startupHomePage1.label;"     value="1"/>
                  <menuitem label="&startupBlankPage.label;"    value="0"/>
                  <menuitem label="&startupLastSession1.label;"  value="3"/>
                </menupopup>
              </menulist>
            </vbox>
            <checkbox_tmp id="resume_from_crash" label="&crashRecovery.enable;" preference="pref_resume_from_crash" class="firefox"/>
            <tabpanel style="margin-top: 30px;" orient="horizontal" flex="1">
              <vbox>
                <label value="&ss.advanced_setting;" class="font-bold"
                        style="text-decoration: underline;color: #CC0000;"/>
                <description class="font-bold">&ss.advanced_setting.warning;</description>
                <label control="ss_timeinterval" value="&ss.interval;"/>
                <hbox align="center" pack="end">
                  <label control="ss_timeinterval" value="&ss.interval.seconds;:"/>
                  <html:input id="ss_timeinterval" preference="pref_ss_timeinterval"
                            maxlength="6" size="6" type="number" required="required" min="0"/>
                </hbox>
                <label control="ss_postdata" value="&ss.privacy_level;:"/>
                <hbox pack="end">
                  <menulist id="ss_postdata" preference="pref_ss_postdata">
                    <menupopup>
                      <menuitem label="&ss.privacy_level.allsites;"    value="0"/>
                      <menuitem label="&ss.privacy_level.unencrypted;" value="1"/>
                      <menuitem label="&ss.privacy_level.nosites;"     value="2"/>
                    </menupopup>
                  </menulist>
                </hbox>
              </vbox>
            </tabpanel>
          </html:fieldset>
        </tabpanel>
      </tabpanels>
    </tabbox>

    <broadcasterset id="paneSession:Broadcaster">
      <broadcaster id="obs_onRestore.overwritewindows"/>
    </broadcasterset>

  </prefpane>

</overlay>
