extensions.{dc572301-7619-498c-a57d-39143191b318}.description=Navegación con pestañas mejorada.
tmp.merge.warning.title=Cerrar ventana con pestañas no fusionadas
tmp.merge.warning.message=Las pestañas seleccionadas ser fusionarán en otra ventana, pero las restantes se cerrarán con la ventana actual.
tmp.merge.warning.checkbox=Avisar siempre antes de cerrar una ventana con pestañas que no se están fusionando.
tmp.merge.error=Tiene que haber al menos dos ventanas abiertas para poder fusionarlas.
tmp.merge.private=No puede fusionar una ventana privada y una no privada.
tmp.importPref.error1=No se pudo importar por no ser un archivo válido.
tmp.importPref.error2=No se pudieron importar las predisposiciones.
tmp.sessionempty=La próxima vez que inicie el navegador, la Última sesión estará vacía.
droptoclose.label=Depositar sobre una pestaña para cerrarla
flstOn.label=Poner en primer plano la última seleccionada - Pulse F9 para cambiar la preferencia
flstOff.label=Foco pestañas a la derecha - Pulse F9 para cambiar la preferencia
slideshowOn.label=Rotación pestañas activada - Pulse F8 para desactivarla
slideshowOff.label=Rotación pestañas desactivada - Pulse F8 para activarla
undoclosetab.keepOpen.label=Mantener menú abierto
undoclosetab.keepOpen.description=pulse para conmutar
undoclosetab.clear.label=Cerrar lista de pestañas cerradas.
undoclosetab.clear.accesskey=C
undoClosedWindows.clear.label=Vaciar la lista de pestañas cerradas
undoClosedWindows.clear.accesskey=V
protectedtabs.closeWarning.1=Va a cerrar %S pestaña protegida.
protectedtabs.closeWarning.2=Va a cerrar %S pestañas protegidas.
protectedtabs.closeWarning.3=Va a cerrar %S pestañas, de ellas %S protegidas.
protectedtabs.closeWarning.4=¿Está seguro de querer continuar?
protectedtabs.closeWarning.5=Avisar antes de cerrar una ventana con pestañas protegidas
window.closeWarning.2=Avisar antes de cerrar una ventana con varias pestañas
closeWindow.label=Cerrar ventana
confirm_autoreloadPostData_title=¡Atención!
confirm_autoreloadPostData=La página en la que ha intentado activar la recarga automática contiene POSTDATA.\nSi activa la opción de recarga automática, cualquier acción que realice el formulario (por ejemplo, un compra online) se repetirá varias veces.\n\n¿Está seguro de querer activar la recarga automática?
confirm_autoreloadPostData_remote=La página en la que ha intentado activar la recarga automática contiene POSTDATA.\nSi activa la opción de recarga automática, cualquier acción que realice el formulario (por ejemplo, un compra online) se repetirá varias veces.\n\n¿Está seguro de querer activar la recarga automática?
incompatible.title=Tab Mix Plus
incompatible.msg0=Las siguientes extensiones están integradas con Tab Mix Plus o son incompatibles con ella.
incompatible.msg1=¿Quiere desactivar esas extensiones?
incompatible.msg2=Las extensiones incompatibles estarán desactivadas cuando reinicie su navegador.
incompatible.button0.label=Desactivar
incompatible.button0.accesskey=D
incompatible.button1.label=No desactivar
incompatible.button1.accesskey=N
incompatible.button2.label=Desactivar y reiniciar
incompatible.button2.accesskey=E
incompatible.chkbox.label=Mostrar este aviso de alerta cuando se inicie el navegador
tabmixoption.error.title=Error de Tabmix
tabmixoption.error.msg=Debe tener una ventana de navegador para administrar las opciones de Tab Mix Plus
# LOCALIZATION NOTE (rowsTooltip.rowscount):
# Semicolon-separated list of plural forms. See:
# http://developer.mozilla.org/en/docs/Localization_and_Plurals
# #1 is the total number of rows
# The singular form is not considered since this string is used only for
# multiple rows.
rowsTooltip.rowscount=;#1 filas
rowsTooltip.activetab=Pestaña activa en fila #1
bugReport.label=Informar de un error
