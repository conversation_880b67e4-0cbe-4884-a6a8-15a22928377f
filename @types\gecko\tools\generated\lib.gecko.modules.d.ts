// don't check the imported files
// @ts-nocheck

/**
 * NOTE: Do not modify this file by hand.
 * Content was generated by running "mach ts paths".
 */

export interface LazyModules {
  "chrome://browser/content/backup/backup-constants.mjs": typeof import("chrome://browser/content/backup/backup-constants.mjs"),
  "chrome://browser/content/migration/migration-wizard-constants.mjs": typeof import("chrome://browser/content/migration/migration-wizard-constants.mjs"),
  "chrome://browser/content/nsContextMenu.sys.mjs": typeof import("chrome://browser/content/nsContextMenu.sys.mjs"),
  "chrome://devtools-startup/content/DevToolsShim.sys.mjs": typeof import("chrome://devtools-startup/content/DevToolsShim.sys.mjs"),
  "chrome://global/content/ml/BlockWords.sys.mjs": typeof import("chrome://global/content/ml/BlockWords.sys.mjs"),
  "chrome://global/content/ml/EmbeddingsGenerator.sys.mjs": typeof import("chrome://global/content/ml/EmbeddingsGenerator.sys.mjs"),
  "chrome://global/content/ml/EngineProcess.sys.mjs": typeof import("chrome://global/content/ml/EngineProcess.sys.mjs"),
  "chrome://global/content/ml/HttpInference.sys.mjs": typeof import("chrome://global/content/ml/HttpInference.sys.mjs"),
  "chrome://global/content/ml/ModelHub.sys.mjs": typeof import("chrome://global/content/ml/ModelHub.sys.mjs"),
  "chrome://global/content/ml/OPFS.sys.mjs": typeof import("chrome://global/content/ml/OPFS.sys.mjs"),
  "chrome://global/content/ml/Utils.sys.mjs": typeof import("chrome://global/content/ml/Utils.sys.mjs"),
  "chrome://global/content/ml/backends/LlamaPipeline.mjs": typeof import("chrome://global/content/ml/backends/LlamaPipeline.mjs"),
  "chrome://global/content/ml/backends/ONNXPipeline.mjs": typeof import("chrome://global/content/ml/backends/ONNXPipeline.mjs"),
  "chrome://global/content/ml/backends/Pipeline.mjs": typeof import("chrome://global/content/ml/backends/Pipeline.mjs"),
  "chrome://global/content/translations/TranslationsTelemetry.sys.mjs": typeof import("chrome://global/content/translations/TranslationsTelemetry.sys.mjs"),
  "chrome://global/content/translations/TranslationsUtils.mjs": typeof import("chrome://global/content/translations/TranslationsUtils.mjs"),
  "chrome://global/content/translations/translations-document.sys.mjs": typeof import("chrome://global/content/translations/translations-document.sys.mjs"),
  "chrome://global/content/translations/translations-engine.sys.mjs": typeof import("chrome://global/content/translations/translations-engine.sys.mjs"),
  "chrome://mochitests/content/browser/remote/shared/messagehandler/test/browser/resources/modules/ModuleRegistry.sys.mjs": typeof import("chrome://mochitests/content/browser/remote/shared/messagehandler/test/browser/resources/modules/ModuleRegistry.sys.mjs"),
  "chrome://pocket/content/Pocket.sys.mjs": typeof import("chrome://pocket/content/Pocket.sys.mjs"),
  "chrome://pocket/content/SaveToPocket.sys.mjs": typeof import("chrome://pocket/content/SaveToPocket.sys.mjs"),
  "chrome://pocket/content/pktApi.sys.mjs": typeof import("chrome://pocket/content/pktApi.sys.mjs"),
  "chrome://pocket/content/pktTelemetry.sys.mjs": typeof import("chrome://pocket/content/pktTelemetry.sys.mjs"),
  "chrome://remote/content/components/Marionette.sys.mjs": typeof import("chrome://remote/content/components/Marionette.sys.mjs"),
  "chrome://remote/content/components/RemoteAgent.sys.mjs": typeof import("chrome://remote/content/components/RemoteAgent.sys.mjs"),
  "chrome://remote/content/marionette/actors/MarionetteCommandsParent.sys.mjs": typeof import("chrome://remote/content/marionette/actors/MarionetteCommandsParent.sys.mjs"),
  "chrome://remote/content/marionette/actors/MarionetteEventsParent.sys.mjs": typeof import("chrome://remote/content/marionette/actors/MarionetteEventsParent.sys.mjs"),
  "chrome://remote/content/marionette/atom.sys.mjs": typeof import("chrome://remote/content/marionette/atom.sys.mjs"),
  "chrome://remote/content/marionette/browser.sys.mjs": typeof import("chrome://remote/content/marionette/browser.sys.mjs"),
  "chrome://remote/content/marionette/cookie.sys.mjs": typeof import("chrome://remote/content/marionette/cookie.sys.mjs"),
  "chrome://remote/content/marionette/driver.sys.mjs": typeof import("chrome://remote/content/marionette/driver.sys.mjs"),
  "chrome://remote/content/marionette/evaluate.sys.mjs": typeof import("chrome://remote/content/marionette/evaluate.sys.mjs"),
  "chrome://remote/content/marionette/interaction.sys.mjs": typeof import("chrome://remote/content/marionette/interaction.sys.mjs"),
  "chrome://remote/content/marionette/json.sys.mjs": typeof import("chrome://remote/content/marionette/json.sys.mjs"),
  "chrome://remote/content/marionette/l10n.sys.mjs": typeof import("chrome://remote/content/marionette/l10n.sys.mjs"),
  "chrome://remote/content/marionette/message.sys.mjs": typeof import("chrome://remote/content/marionette/message.sys.mjs"),
  "chrome://remote/content/marionette/navigate.sys.mjs": typeof import("chrome://remote/content/marionette/navigate.sys.mjs"),
  "chrome://remote/content/marionette/packets.sys.mjs": typeof import("chrome://remote/content/marionette/packets.sys.mjs"),
  "chrome://remote/content/marionette/prefs.sys.mjs": typeof import("chrome://remote/content/marionette/prefs.sys.mjs"),
  "chrome://remote/content/marionette/reftest.sys.mjs": typeof import("chrome://remote/content/marionette/reftest.sys.mjs"),
  "chrome://remote/content/marionette/server.sys.mjs": typeof import("chrome://remote/content/marionette/server.sys.mjs"),
  "chrome://remote/content/marionette/stream-utils.sys.mjs": typeof import("chrome://remote/content/marionette/stream-utils.sys.mjs"),
  "chrome://remote/content/marionette/sync.sys.mjs": typeof import("chrome://remote/content/marionette/sync.sys.mjs"),
  "chrome://remote/content/marionette/transport.sys.mjs": typeof import("chrome://remote/content/marionette/transport.sys.mjs"),
  "chrome://remote/content/marionette/web-reference.sys.mjs": typeof import("chrome://remote/content/marionette/web-reference.sys.mjs"),
  "chrome://remote/content/marionette/webauthn.sys.mjs": typeof import("chrome://remote/content/marionette/webauthn.sys.mjs"),
  "chrome://remote/content/server/WebSocketHandshake.sys.mjs": typeof import("chrome://remote/content/server/WebSocketHandshake.sys.mjs"),
  "chrome://remote/content/server/WebSocketTransport.sys.mjs": typeof import("chrome://remote/content/server/WebSocketTransport.sys.mjs"),
  "chrome://remote/content/server/httpd.sys.mjs": typeof import("chrome://remote/content/server/httpd.sys.mjs"),
  "chrome://remote/content/shared/Addon.sys.mjs": typeof import("chrome://remote/content/shared/Addon.sys.mjs"),
  "chrome://remote/content/shared/AppInfo.sys.mjs": typeof import("chrome://remote/content/shared/AppInfo.sys.mjs"),
  "chrome://remote/content/shared/AsyncQueue.sys.mjs": typeof import("chrome://remote/content/shared/AsyncQueue.sys.mjs"),
  "chrome://remote/content/shared/Browser.sys.mjs": typeof import("chrome://remote/content/shared/Browser.sys.mjs"),
  "chrome://remote/content/shared/Capture.sys.mjs": typeof import("chrome://remote/content/shared/Capture.sys.mjs"),
  "chrome://remote/content/shared/ChallengeHeaderParser.sys.mjs": typeof import("chrome://remote/content/shared/ChallengeHeaderParser.sys.mjs"),
  "chrome://remote/content/shared/DOM.sys.mjs": typeof import("chrome://remote/content/shared/DOM.sys.mjs"),
  "chrome://remote/content/shared/Format.sys.mjs": typeof import("chrome://remote/content/shared/Format.sys.mjs"),
  "chrome://remote/content/shared/Log.sys.mjs": typeof import("chrome://remote/content/shared/Log.sys.mjs"),
  "chrome://remote/content/shared/MobileTabBrowser.sys.mjs": typeof import("chrome://remote/content/shared/MobileTabBrowser.sys.mjs"),
  "chrome://remote/content/shared/Navigate.sys.mjs": typeof import("chrome://remote/content/shared/Navigate.sys.mjs"),
  "chrome://remote/content/shared/NavigationManager.sys.mjs": typeof import("chrome://remote/content/shared/NavigationManager.sys.mjs"),
  "chrome://remote/content/shared/NetworkCacheManager.sys.mjs": typeof import("chrome://remote/content/shared/NetworkCacheManager.sys.mjs"),
  "chrome://remote/content/shared/NetworkDecodedBodySizeMap.sys.mjs": typeof import("chrome://remote/content/shared/NetworkDecodedBodySizeMap.sys.mjs"),
  "chrome://remote/content/shared/NetworkRequest.sys.mjs": typeof import("chrome://remote/content/shared/NetworkRequest.sys.mjs"),
  "chrome://remote/content/shared/NetworkResponse.sys.mjs": typeof import("chrome://remote/content/shared/NetworkResponse.sys.mjs"),
  "chrome://remote/content/shared/PDF.sys.mjs": typeof import("chrome://remote/content/shared/PDF.sys.mjs"),
  "chrome://remote/content/shared/Permissions.sys.mjs": typeof import("chrome://remote/content/shared/Permissions.sys.mjs"),
  "chrome://remote/content/shared/Prompt.sys.mjs": typeof import("chrome://remote/content/shared/Prompt.sys.mjs"),
  "chrome://remote/content/shared/Realm.sys.mjs": typeof import("chrome://remote/content/shared/Realm.sys.mjs"),
  "chrome://remote/content/shared/RecommendedPreferences.sys.mjs": typeof import("chrome://remote/content/shared/RecommendedPreferences.sys.mjs"),
  "chrome://remote/content/shared/Stack.sys.mjs": typeof import("chrome://remote/content/shared/Stack.sys.mjs"),
  "chrome://remote/content/shared/Sync.sys.mjs": typeof import("chrome://remote/content/shared/Sync.sys.mjs"),
  "chrome://remote/content/shared/TabManager.sys.mjs": typeof import("chrome://remote/content/shared/TabManager.sys.mjs"),
  "chrome://remote/content/shared/UUID.sys.mjs": typeof import("chrome://remote/content/shared/UUID.sys.mjs"),
  "chrome://remote/content/shared/UserContextManager.sys.mjs": typeof import("chrome://remote/content/shared/UserContextManager.sys.mjs"),
  "chrome://remote/content/shared/WindowManager.sys.mjs": typeof import("chrome://remote/content/shared/WindowManager.sys.mjs"),
  "chrome://remote/content/shared/js-process-actors/WebDriverDocumentInsertedActor.sys.mjs": typeof import("chrome://remote/content/shared/js-process-actors/WebDriverDocumentInsertedActor.sys.mjs"),
  "chrome://remote/content/shared/js-window-actors/WebProgressListenerActor.sys.mjs": typeof import("chrome://remote/content/shared/js-window-actors/WebProgressListenerActor.sys.mjs"),
  "chrome://remote/content/shared/listeners/BeforeStopRequestListener.sys.mjs": typeof import("chrome://remote/content/shared/listeners/BeforeStopRequestListener.sys.mjs"),
  "chrome://remote/content/shared/listeners/BrowsingContextListener.sys.mjs": typeof import("chrome://remote/content/shared/listeners/BrowsingContextListener.sys.mjs"),
  "chrome://remote/content/shared/listeners/CachedResourceListener.sys.mjs": typeof import("chrome://remote/content/shared/listeners/CachedResourceListener.sys.mjs"),
  "chrome://remote/content/shared/listeners/ConsoleAPIListener.sys.mjs": typeof import("chrome://remote/content/shared/listeners/ConsoleAPIListener.sys.mjs"),
  "chrome://remote/content/shared/listeners/ConsoleListener.sys.mjs": typeof import("chrome://remote/content/shared/listeners/ConsoleListener.sys.mjs"),
  "chrome://remote/content/shared/listeners/ContextualIdentityListener.sys.mjs": typeof import("chrome://remote/content/shared/listeners/ContextualIdentityListener.sys.mjs"),
  "chrome://remote/content/shared/listeners/DataChannelListener.sys.mjs": typeof import("chrome://remote/content/shared/listeners/DataChannelListener.sys.mjs"),
  "chrome://remote/content/shared/listeners/LoadListener.sys.mjs": typeof import("chrome://remote/content/shared/listeners/LoadListener.sys.mjs"),
  "chrome://remote/content/shared/listeners/NavigationListener.sys.mjs": typeof import("chrome://remote/content/shared/listeners/NavigationListener.sys.mjs"),
  "chrome://remote/content/shared/listeners/NetworkEventRecord.sys.mjs": typeof import("chrome://remote/content/shared/listeners/NetworkEventRecord.sys.mjs"),
  "chrome://remote/content/shared/listeners/NetworkListener.sys.mjs": typeof import("chrome://remote/content/shared/listeners/NetworkListener.sys.mjs"),
  "chrome://remote/content/shared/listeners/ParentWebProgressListener.sys.mjs": typeof import("chrome://remote/content/shared/listeners/ParentWebProgressListener.sys.mjs"),
  "chrome://remote/content/shared/listeners/PromptListener.sys.mjs": typeof import("chrome://remote/content/shared/listeners/PromptListener.sys.mjs"),
  "chrome://remote/content/shared/messagehandler/Errors.sys.mjs": typeof import("chrome://remote/content/shared/messagehandler/Errors.sys.mjs"),
  "chrome://remote/content/shared/messagehandler/EventsDispatcher.sys.mjs": typeof import("chrome://remote/content/shared/messagehandler/EventsDispatcher.sys.mjs"),
  "chrome://remote/content/shared/messagehandler/MessageHandler.sys.mjs": typeof import("chrome://remote/content/shared/messagehandler/MessageHandler.sys.mjs"),
  "chrome://remote/content/shared/messagehandler/MessageHandlerRegistry.sys.mjs": typeof import("chrome://remote/content/shared/messagehandler/MessageHandlerRegistry.sys.mjs"),
  "chrome://remote/content/shared/messagehandler/ModuleCache.sys.mjs": typeof import("chrome://remote/content/shared/messagehandler/ModuleCache.sys.mjs"),
  "chrome://remote/content/shared/messagehandler/RootMessageHandler.sys.mjs": typeof import("chrome://remote/content/shared/messagehandler/RootMessageHandler.sys.mjs"),
  "chrome://remote/content/shared/messagehandler/RootMessageHandlerRegistry.sys.mjs": typeof import("chrome://remote/content/shared/messagehandler/RootMessageHandlerRegistry.sys.mjs"),
  "chrome://remote/content/shared/messagehandler/WindowGlobalMessageHandler.sys.mjs": typeof import("chrome://remote/content/shared/messagehandler/WindowGlobalMessageHandler.sys.mjs"),
  "chrome://remote/content/shared/messagehandler/sessiondata/SessionData.sys.mjs": typeof import("chrome://remote/content/shared/messagehandler/sessiondata/SessionData.sys.mjs"),
  "chrome://remote/content/shared/messagehandler/sessiondata/SessionDataReader.sys.mjs": typeof import("chrome://remote/content/shared/messagehandler/sessiondata/SessionDataReader.sys.mjs"),
  "chrome://remote/content/shared/messagehandler/transports/BrowsingContextUtils.sys.mjs": typeof import("chrome://remote/content/shared/messagehandler/transports/BrowsingContextUtils.sys.mjs"),
  "chrome://remote/content/shared/messagehandler/transports/RootTransport.sys.mjs": typeof import("chrome://remote/content/shared/messagehandler/transports/RootTransport.sys.mjs"),
  "chrome://remote/content/shared/messagehandler/transports/js-window-actors/MessageHandlerFrameActor.sys.mjs": typeof import("chrome://remote/content/shared/messagehandler/transports/js-window-actors/MessageHandlerFrameActor.sys.mjs"),
  "chrome://remote/content/shared/messagehandler/transports/js-window-actors/MessageHandlerFrameChild.sys.mjs": typeof import("chrome://remote/content/shared/messagehandler/transports/js-window-actors/MessageHandlerFrameChild.sys.mjs"),
  "chrome://remote/content/shared/webdriver/Accessibility.sys.mjs": typeof import("chrome://remote/content/shared/webdriver/Accessibility.sys.mjs"),
  "chrome://remote/content/shared/webdriver/Actions.sys.mjs": typeof import("chrome://remote/content/shared/webdriver/Actions.sys.mjs"),
  "chrome://remote/content/shared/webdriver/Assert.sys.mjs": typeof import("chrome://remote/content/shared/webdriver/Assert.sys.mjs"),
  "chrome://remote/content/shared/webdriver/Capabilities.sys.mjs": typeof import("chrome://remote/content/shared/webdriver/Capabilities.sys.mjs"),
  "chrome://remote/content/shared/webdriver/Certificates.sys.mjs": typeof import("chrome://remote/content/shared/webdriver/Certificates.sys.mjs"),
  "chrome://remote/content/shared/webdriver/Errors.sys.mjs": typeof import("chrome://remote/content/shared/webdriver/Errors.sys.mjs"),
  "chrome://remote/content/shared/webdriver/Event.sys.mjs": typeof import("chrome://remote/content/shared/webdriver/Event.sys.mjs"),
  "chrome://remote/content/shared/webdriver/KeyData.sys.mjs": typeof import("chrome://remote/content/shared/webdriver/KeyData.sys.mjs"),
  "chrome://remote/content/shared/webdriver/NodeCache.sys.mjs": typeof import("chrome://remote/content/shared/webdriver/NodeCache.sys.mjs"),
  "chrome://remote/content/shared/webdriver/Session.sys.mjs": typeof import("chrome://remote/content/shared/webdriver/Session.sys.mjs"),
  "chrome://remote/content/shared/webdriver/URLPattern.sys.mjs": typeof import("chrome://remote/content/shared/webdriver/URLPattern.sys.mjs"),
  "chrome://remote/content/shared/webdriver/UserPromptHandler.sys.mjs": typeof import("chrome://remote/content/shared/webdriver/UserPromptHandler.sys.mjs"),
  "chrome://remote/content/shared/webdriver/process-actors/WebDriverProcessDataParent.sys.mjs": typeof import("chrome://remote/content/shared/webdriver/process-actors/WebDriverProcessDataParent.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/NewSessionHandler.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/NewSessionHandler.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/ProxyPerUserContextManager.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/ProxyPerUserContextManager.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/RemoteValue.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/RemoteValue.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/WebDriverBiDi.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/WebDriverBiDi.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/WebDriverBiDiConnection.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/WebDriverBiDiConnection.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/Intercept.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/Intercept.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/ModuleRegistry.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/ModuleRegistry.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/root/browser.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/root/browser.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/root/browsingContext.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/root/browsingContext.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/root/emulation.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/root/emulation.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/root/input.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/root/input.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/root/log.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/root/log.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/root/network.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/root/network.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/root/permissions.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/root/permissions.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/root/script.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/root/script.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/root/session.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/root/session.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/root/storage.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/root/storage.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/root/webExtension.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/root/webExtension.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/windowglobal-in-root/browsingContext.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/windowglobal-in-root/browsingContext.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/windowglobal-in-root/log.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/windowglobal-in-root/log.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/windowglobal-in-root/network.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/windowglobal-in-root/network.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/windowglobal-in-root/script.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/windowglobal-in-root/script.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/windowglobal/_configuration.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/windowglobal/_configuration.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/windowglobal/browsingContext.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/windowglobal/browsingContext.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/windowglobal/emulation.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/windowglobal/emulation.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/windowglobal/input.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/windowglobal/input.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/windowglobal/log.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/windowglobal/log.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/windowglobal/network.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/windowglobal/network.sys.mjs"),
  "chrome://remote/content/webdriver-bidi/modules/windowglobal/script.sys.mjs": typeof import("chrome://remote/content/webdriver-bidi/modules/windowglobal/script.sys.mjs"),
  "moz-src:///browser/components/DefaultBrowserCheck.sys.mjs": typeof import("moz-src:///browser/components/DefaultBrowserCheck.sys.mjs"),
  "moz-src:///browser/components/DesktopActorRegistry.sys.mjs": typeof import("moz-src:///browser/components/DesktopActorRegistry.sys.mjs"),
  "moz-src:///browser/components/ProfileDataUpgrader.sys.mjs": typeof import("moz-src:///browser/components/ProfileDataUpgrader.sys.mjs"),
  "moz-src:///browser/components/genai/LinkPreview.sys.mjs": typeof import("moz-src:///browser/components/genai/LinkPreview.sys.mjs"),
  "moz-src:///browser/components/genai/LinkPreviewModel.sys.mjs": typeof import("moz-src:///browser/components/genai/LinkPreviewModel.sys.mjs"),
  "moz-src:///browser/components/places/Interactions.sys.mjs": typeof import("moz-src:///browser/components/places/Interactions.sys.mjs"),
  "moz-src:///browser/components/places/InteractionsBlocklist.sys.mjs": typeof import("moz-src:///browser/components/places/InteractionsBlocklist.sys.mjs"),
  "moz-src:///browser/components/places/PlacesBrowserStartup.sys.mjs": typeof import("moz-src:///browser/components/places/PlacesBrowserStartup.sys.mjs"),
  "moz-src:///browser/components/places/PlacesUIUtils.sys.mjs": typeof import("moz-src:///browser/components/places/PlacesUIUtils.sys.mjs"),
  "moz-src:///browser/components/protections/ContentBlockingPrefs.sys.mjs": typeof import("moz-src:///browser/components/protections/ContentBlockingPrefs.sys.mjs"),
  "moz-src:///browser/components/search/BrowserSearchTelemetry.sys.mjs": typeof import("moz-src:///browser/components/search/BrowserSearchTelemetry.sys.mjs"),
  "moz-src:///browser/components/search/OpenSearchManager.sys.mjs": typeof import("moz-src:///browser/components/search/OpenSearchManager.sys.mjs"),
  "moz-src:///browser/components/search/SERPCategorization.sys.mjs": typeof import("moz-src:///browser/components/search/SERPCategorization.sys.mjs"),
  "moz-src:///browser/components/search/SearchOneOffs.sys.mjs": typeof import("moz-src:///browser/components/search/SearchOneOffs.sys.mjs"),
  "moz-src:///browser/components/search/SearchSERPTelemetry.sys.mjs": typeof import("moz-src:///browser/components/search/SearchSERPTelemetry.sys.mjs"),
  "moz-src:///browser/components/search/SearchUIUtils.sys.mjs": typeof import("moz-src:///browser/components/search/SearchUIUtils.sys.mjs"),
  "moz-src:///browser/components/shell/StartupOSIntegration.sys.mjs": typeof import("moz-src:///browser/components/shell/StartupOSIntegration.sys.mjs"),
  "moz-src:///browser/components/sidebar/SidebarManager.sys.mjs": typeof import("moz-src:///browser/components/sidebar/SidebarManager.sys.mjs"),
  "moz-src:///browser/components/sidebar/SidebarState.sys.mjs": typeof import("moz-src:///browser/components/sidebar/SidebarState.sys.mjs"),
  "moz-src:///browser/components/tabbrowser/AsyncTabSwitcher.sys.mjs": typeof import("moz-src:///browser/components/tabbrowser/AsyncTabSwitcher.sys.mjs"),
  "moz-src:///browser/components/tabbrowser/GroupsList.sys.mjs": typeof import("moz-src:///browser/components/tabbrowser/GroupsList.sys.mjs"),
  "moz-src:///browser/components/tabbrowser/NewTabPagePreloading.sys.mjs": typeof import("moz-src:///browser/components/tabbrowser/NewTabPagePreloading.sys.mjs"),
  "moz-src:///browser/components/tabbrowser/OpenInTabsUtils.sys.mjs": typeof import("moz-src:///browser/components/tabbrowser/OpenInTabsUtils.sys.mjs"),
  "moz-src:///browser/components/tabbrowser/SmartTabGrouping.sys.mjs": typeof import("moz-src:///browser/components/tabbrowser/SmartTabGrouping.sys.mjs"),
  "moz-src:///browser/components/tabbrowser/TabMetrics.sys.mjs": typeof import("moz-src:///browser/components/tabbrowser/TabMetrics.sys.mjs"),
  "moz-src:///browser/components/tabbrowser/TabsList.sys.mjs": typeof import("moz-src:///browser/components/tabbrowser/TabsList.sys.mjs"),
  "moz-src:///browser/components/uitour/UITour.sys.mjs": typeof import("moz-src:///browser/components/uitour/UITour.sys.mjs"),
  "moz-src:///browser/modules/ContextId.sys.mjs": typeof import("moz-src:///browser/modules/ContextId.sys.mjs"),
  "moz-src:///browser/themes/ToolbarIconColor.sys.mjs": typeof import("moz-src:///browser/themes/ToolbarIconColor.sys.mjs"),
  "moz-src:///toolkit/components/reader/AboutReader.sys.mjs": typeof import("moz-src:///toolkit/components/reader/AboutReader.sys.mjs"),
  "moz-src:///toolkit/components/reader/ReaderMode.sys.mjs": typeof import("moz-src:///toolkit/components/reader/ReaderMode.sys.mjs"),
  "moz-src:///toolkit/components/reader/ReaderWorker.sys.mjs": typeof import("moz-src:///toolkit/components/reader/ReaderWorker.sys.mjs"),
  "moz-src:///toolkit/components/search/AddonSearchEngine.sys.mjs": typeof import("moz-src:///toolkit/components/search/AddonSearchEngine.sys.mjs"),
  "moz-src:///toolkit/components/search/AppProvidedSearchEngine.sys.mjs": typeof import("moz-src:///toolkit/components/search/AppProvidedSearchEngine.sys.mjs"),
  "moz-src:///toolkit/components/search/OpenSearchEngine.sys.mjs": typeof import("moz-src:///toolkit/components/search/OpenSearchEngine.sys.mjs"),
  "moz-src:///toolkit/components/search/OpenSearchLoader.sys.mjs": typeof import("moz-src:///toolkit/components/search/OpenSearchLoader.sys.mjs"),
  "moz-src:///toolkit/components/search/PolicySearchEngine.sys.mjs": typeof import("moz-src:///toolkit/components/search/PolicySearchEngine.sys.mjs"),
  "moz-src:///toolkit/components/search/SearchEngine.sys.mjs": typeof import("moz-src:///toolkit/components/search/SearchEngine.sys.mjs"),
  "moz-src:///toolkit/components/search/SearchEngineSelector.sys.mjs": typeof import("moz-src:///toolkit/components/search/SearchEngineSelector.sys.mjs"),
  "moz-src:///toolkit/components/search/SearchSettings.sys.mjs": typeof import("moz-src:///toolkit/components/search/SearchSettings.sys.mjs"),
  "moz-src:///toolkit/components/search/SearchShortcuts.sys.mjs": typeof import("moz-src:///toolkit/components/search/SearchShortcuts.sys.mjs"),
  "moz-src:///toolkit/components/search/SearchStaticData.sys.mjs": typeof import("moz-src:///toolkit/components/search/SearchStaticData.sys.mjs"),
  "moz-src:///toolkit/components/search/SearchSuggestionController.sys.mjs": typeof import("moz-src:///toolkit/components/search/SearchSuggestionController.sys.mjs"),
  "moz-src:///toolkit/components/search/SearchUtils.sys.mjs": typeof import("moz-src:///toolkit/components/search/SearchUtils.sys.mjs"),
  "moz-src:///toolkit/components/search/UserSearchEngine.sys.mjs": typeof import("moz-src:///toolkit/components/search/UserSearchEngine.sys.mjs"),
  "moz-src:///toolkit/components/uniffi-bindgen-gecko-js/components/generated/RustRelevancy.sys.mjs": typeof import("moz-src:///toolkit/components/uniffi-bindgen-gecko-js/components/generated/RustRelevancy.sys.mjs"),
  "moz-src:///toolkit/components/uniffi-bindgen-gecko-js/components/generated/RustSearch.sys.mjs": typeof import("moz-src:///toolkit/components/uniffi-bindgen-gecko-js/components/generated/RustSearch.sys.mjs"),
  "moz-src:///toolkit/components/uniffi-bindgen-gecko-js/components/generated/RustSuggest.sys.mjs": typeof import("moz-src:///toolkit/components/uniffi-bindgen-gecko-js/components/generated/RustSuggest.sys.mjs"),
  "moz-src:///toolkit/components/uniffi-bindgen-gecko-js/components/generated/RustTabs.sys.mjs": typeof import("moz-src:///toolkit/components/uniffi-bindgen-gecko-js/components/generated/RustTabs.sys.mjs"),
  "moz-src:///toolkit/components/uniffi-bindgen-gecko-js/components/generated/RustWebextstorage.sys.mjs": typeof import("moz-src:///toolkit/components/uniffi-bindgen-gecko-js/components/generated/RustWebextstorage.sys.mjs"),
  "moz-src:///toolkit/profile/ProfilesDatastoreService.sys.mjs": typeof import("moz-src:///toolkit/profile/ProfilesDatastoreService.sys.mjs"),
  "resource:///actors/AboutNewTabParent.sys.mjs": typeof import("resource:///actors/AboutNewTabParent.sys.mjs"),
  "resource:///actors/AboutReaderParent.sys.mjs": typeof import("resource:///actors/AboutReaderParent.sys.mjs"),
  "resource:///actors/AboutWelcomeParent.sys.mjs": typeof import("resource:///actors/AboutWelcomeParent.sys.mjs"),
  "resource:///actors/ClickHandlerParent.sys.mjs": typeof import("resource:///actors/ClickHandlerParent.sys.mjs"),
  "resource:///actors/ContentSearchParent.sys.mjs": typeof import("resource:///actors/ContentSearchParent.sys.mjs"),
  "resource:///actors/ContextMenuChild.sys.mjs": typeof import("resource:///actors/ContextMenuChild.sys.mjs"),
  "resource:///actors/LinkHandlerParent.sys.mjs": typeof import("resource:///actors/LinkHandlerParent.sys.mjs"),
  "resource:///actors/PluginParent.sys.mjs": typeof import("resource:///actors/PluginParent.sys.mjs"),
  "resource:///actors/SearchSERPTelemetryChild.sys.mjs": typeof import("resource:///actors/SearchSERPTelemetryChild.sys.mjs"),
  "resource:///actors/WebRTCChild.sys.mjs": typeof import("resource:///actors/WebRTCChild.sys.mjs"),
  "resource:///modules/360seMigrationUtils.sys.mjs": typeof import("resource:///modules/360seMigrationUtils.sys.mjs"),
  "resource:///modules/AboutHomeStartupCache.sys.mjs": typeof import("resource:///modules/AboutHomeStartupCache.sys.mjs"),
  "resource:///modules/AboutNewTab.sys.mjs": typeof import("resource:///modules/AboutNewTab.sys.mjs"),
  "resource:///modules/AboutNewTabRedirector.sys.mjs": typeof import("resource:///modules/AboutNewTabRedirector.sys.mjs"),
  "resource:///modules/ActionsProviderContextualSearch.sys.mjs": typeof import("resource:///modules/ActionsProviderContextualSearch.sys.mjs"),
  "resource:///modules/ActionsProviderQuickActions.sys.mjs": typeof import("resource:///modules/ActionsProviderQuickActions.sys.mjs"),
  "resource:///modules/AttributionCode.sys.mjs": typeof import("resource:///modules/AttributionCode.sys.mjs"),
  "resource:///modules/BrowserGlue.sys.mjs": typeof import("resource:///modules/BrowserGlue.sys.mjs"),
  "resource:///modules/BrowserUIUtils.sys.mjs": typeof import("resource:///modules/BrowserUIUtils.sys.mjs"),
  "resource:///modules/BrowserUsageTelemetry.sys.mjs": typeof import("resource:///modules/BrowserUsageTelemetry.sys.mjs"),
  "resource:///modules/BrowserWindowTracker.sys.mjs": typeof import("resource:///modules/BrowserWindowTracker.sys.mjs"),
  "resource:///modules/BuiltInThemeConfig.sys.mjs": typeof import("resource:///modules/BuiltInThemeConfig.sys.mjs"),
  "resource:///modules/BuiltInThemes.sys.mjs": typeof import("resource:///modules/BuiltInThemes.sys.mjs"),
  "resource:///modules/ChromeMigrationUtils.sys.mjs": typeof import("resource:///modules/ChromeMigrationUtils.sys.mjs"),
  "resource:///modules/ContentAnalysis.sys.mjs": typeof import("resource:///modules/ContentAnalysis.sys.mjs"),
  "resource:///modules/ContentCrashHandlers.sys.mjs": typeof import("resource:///modules/ContentCrashHandlers.sys.mjs"),
  "resource:///modules/CustomizableUI.sys.mjs": typeof import("resource:///modules/CustomizableUI.sys.mjs"),
  "resource:///modules/CustomizableWidgets.sys.mjs": typeof import("resource:///modules/CustomizableWidgets.sys.mjs"),
  "resource:///modules/Discovery.sys.mjs": typeof import("resource:///modules/Discovery.sys.mjs"),
  "resource:///modules/DownloadSpamProtection.sys.mjs": typeof import("resource:///modules/DownloadSpamProtection.sys.mjs"),
  "resource:///modules/DownloadsCommon.sys.mjs": typeof import("resource:///modules/DownloadsCommon.sys.mjs"),
  "resource:///modules/DownloadsViewUI.sys.mjs": typeof import("resource:///modules/DownloadsViewUI.sys.mjs"),
  "resource:///modules/DownloadsViewableInternally.sys.mjs": typeof import("resource:///modules/DownloadsViewableInternally.sys.mjs"),
  "resource:///modules/DragPositionManager.sys.mjs": typeof import("resource:///modules/DragPositionManager.sys.mjs"),
  "resource:///modules/ESEDBReader.sys.mjs": typeof import("resource:///modules/ESEDBReader.sys.mjs"),
  "resource:///modules/EveryWindow.sys.mjs": typeof import("resource:///modules/EveryWindow.sys.mjs"),
  "resource:///modules/ExtensionBrowsingData.sys.mjs": typeof import("resource:///modules/ExtensionBrowsingData.sys.mjs"),
  "resource:///modules/ExtensionControlledPopup.sys.mjs": typeof import("resource:///modules/ExtensionControlledPopup.sys.mjs"),
  "resource:///modules/ExtensionPopups.sys.mjs": typeof import("resource:///modules/ExtensionPopups.sys.mjs"),
  "resource:///modules/ExtensionsUI.sys.mjs": typeof import("resource:///modules/ExtensionsUI.sys.mjs"),
  "resource:///modules/FaviconLoader.sys.mjs": typeof import("resource:///modules/FaviconLoader.sys.mjs"),
  "resource:///modules/FileMigrators.sys.mjs": typeof import("resource:///modules/FileMigrators.sys.mjs"),
  "resource:///modules/FilePickerCrashed.sys.mjs": typeof import("resource:///modules/FilePickerCrashed.sys.mjs"),
  "resource:///modules/FilterAdult.sys.mjs": typeof import("resource:///modules/FilterAdult.sys.mjs"),
  "resource:///modules/FirefoxBridgeExtensionUtils.sys.mjs": typeof import("resource:///modules/FirefoxBridgeExtensionUtils.sys.mjs"),
  "resource:///modules/FirefoxProfileMigrator.sys.mjs": typeof import("resource:///modules/FirefoxProfileMigrator.sys.mjs"),
  "resource:///modules/GenAI.sys.mjs": typeof import("resource:///modules/GenAI.sys.mjs"),
  "resource:///modules/HeadlessShell.sys.mjs": typeof import("resource:///modules/HeadlessShell.sys.mjs"),
  "resource:///modules/HistoryController.sys.mjs": typeof import("resource:///modules/HistoryController.sys.mjs"),
  "resource:///modules/HomePage.sys.mjs": typeof import("resource:///modules/HomePage.sys.mjs"),
  "resource:///modules/InternalTestingProfileMigrator.sys.mjs": typeof import("resource:///modules/InternalTestingProfileMigrator.sys.mjs"),
  "resource:///modules/LaterRun.sys.mjs": typeof import("resource:///modules/LaterRun.sys.mjs"),
  "resource:///modules/LinksCache.sys.mjs": typeof import("resource:///modules/LinksCache.sys.mjs"),
  "resource:///modules/LoginBreaches.sys.mjs": typeof import("resource:///modules/LoginBreaches.sys.mjs"),
  "resource:///modules/MacAttribution.sys.mjs": typeof import("resource:///modules/MacAttribution.sys.mjs"),
  "resource:///modules/MerinoClient.sys.mjs": typeof import("resource:///modules/MerinoClient.sys.mjs"),
  "resource:///modules/MigrationUtils.sys.mjs": typeof import("resource:///modules/MigrationUtils.sys.mjs"),
  "resource:///modules/OpenTabs.sys.mjs": typeof import("resource:///modules/OpenTabs.sys.mjs"),
  "resource:///modules/PageActions.sys.mjs": typeof import("resource:///modules/PageActions.sys.mjs"),
  "resource:///modules/PanelMultiView.sys.mjs": typeof import("resource:///modules/PanelMultiView.sys.mjs"),
  "resource:///modules/PartnerLinkAttribution.sys.mjs": typeof import("resource:///modules/PartnerLinkAttribution.sys.mjs"),
  "resource:///modules/PermissionUI.sys.mjs": typeof import("resource:///modules/PermissionUI.sys.mjs"),
  "resource:///modules/PopupBlockerObserver.sys.mjs": typeof import("resource:///modules/PopupBlockerObserver.sys.mjs"),
  "resource:///modules/ProcessHangMonitor.sys.mjs": typeof import("resource:///modules/ProcessHangMonitor.sys.mjs"),
  "resource:///modules/QuickActionsLoaderDefault.sys.mjs": typeof import("resource:///modules/QuickActionsLoaderDefault.sys.mjs"),
  "resource:///modules/QuickSuggest.sys.mjs": typeof import("resource:///modules/QuickSuggest.sys.mjs"),
  "resource:///modules/ReportBrokenSite.sys.mjs": typeof import("resource:///modules/ReportBrokenSite.sys.mjs"),
  "resource:///modules/ResetPBMPanel.sys.mjs": typeof import("resource:///modules/ResetPBMPanel.sys.mjs"),
  "resource:///modules/SafariProfileMigrator.sys.mjs": typeof import("resource:///modules/SafariProfileMigrator.sys.mjs"),
  "resource:///modules/Sanitizer.sys.mjs": typeof import("resource:///modules/Sanitizer.sys.mjs"),
  "resource:///modules/ScreenshotsOverlayChild.sys.mjs": typeof import("resource:///modules/ScreenshotsOverlayChild.sys.mjs"),
  "resource:///modules/ScreenshotsUtils.sys.mjs": typeof import("resource:///modules/ScreenshotsUtils.sys.mjs"),
  "resource:///modules/SearchModeSwitcher.sys.mjs": typeof import("resource:///modules/SearchModeSwitcher.sys.mjs"),
  "resource:///modules/SelectionChangedMenulist.sys.mjs": typeof import("resource:///modules/SelectionChangedMenulist.sys.mjs"),
  "resource:///modules/SharingUtils.sys.mjs": typeof import("resource:///modules/SharingUtils.sys.mjs"),
  "resource:///modules/ShellService.sys.mjs": typeof import("resource:///modules/ShellService.sys.mjs"),
  "resource:///modules/SiteDataManager.sys.mjs": typeof import("resource:///modules/SiteDataManager.sys.mjs"),
  "resource:///modules/SitePermissions.sys.mjs": typeof import("resource:///modules/SitePermissions.sys.mjs"),
  "resource:///modules/SyncedTabsController.sys.mjs": typeof import("resource:///modules/SyncedTabsController.sys.mjs"),
  "resource:///modules/TRRPerformance.sys.mjs": typeof import("resource:///modules/TRRPerformance.sys.mjs"),
  "resource:///modules/ThemeVariableMap.sys.mjs": typeof import("resource:///modules/ThemeVariableMap.sys.mjs"),
  "resource:///modules/ToolbarContextMenu.sys.mjs": typeof import("resource:///modules/ToolbarContextMenu.sys.mjs"),
  "resource:///modules/ToolbarDropHandler.sys.mjs": typeof import("resource:///modules/ToolbarDropHandler.sys.mjs"),
  "resource:///modules/TransientPrefs.sys.mjs": typeof import("resource:///modules/TransientPrefs.sys.mjs"),
  "resource:///modules/URILoadingHelper.sys.mjs": typeof import("resource:///modules/URILoadingHelper.sys.mjs"),
  "resource:///modules/UrlbarController.sys.mjs": typeof import("resource:///modules/UrlbarController.sys.mjs"),
  "resource:///modules/UrlbarEventBufferer.sys.mjs": typeof import("resource:///modules/UrlbarEventBufferer.sys.mjs"),
  "resource:///modules/UrlbarInput.sys.mjs": typeof import("resource:///modules/UrlbarInput.sys.mjs"),
  "resource:///modules/UrlbarPrefs.sys.mjs": typeof import("resource:///modules/UrlbarPrefs.sys.mjs"),
  "resource:///modules/UrlbarProviderAutofill.sys.mjs": typeof import("resource:///modules/UrlbarProviderAutofill.sys.mjs"),
  "resource:///modules/UrlbarProviderCalculator.sys.mjs": typeof import("resource:///modules/UrlbarProviderCalculator.sys.mjs"),
  "resource:///modules/UrlbarProviderClipboard.sys.mjs": typeof import("resource:///modules/UrlbarProviderClipboard.sys.mjs"),
  "resource:///modules/UrlbarProviderGlobalActions.sys.mjs": typeof import("resource:///modules/UrlbarProviderGlobalActions.sys.mjs"),
  "resource:///modules/UrlbarProviderInterventions.sys.mjs": typeof import("resource:///modules/UrlbarProviderInterventions.sys.mjs"),
  "resource:///modules/UrlbarProviderOpenTabs.sys.mjs": typeof import("resource:///modules/UrlbarProviderOpenTabs.sys.mjs"),
  "resource:///modules/UrlbarProviderPlaces.sys.mjs": typeof import("resource:///modules/UrlbarProviderPlaces.sys.mjs"),
  "resource:///modules/UrlbarProviderQuickSuggest.sys.mjs": typeof import("resource:///modules/UrlbarProviderQuickSuggest.sys.mjs"),
  "resource:///modules/UrlbarProviderQuickSuggestContextualOptIn.sys.mjs": typeof import("resource:///modules/UrlbarProviderQuickSuggestContextualOptIn.sys.mjs"),
  "resource:///modules/UrlbarProviderRecentSearches.sys.mjs": typeof import("resource:///modules/UrlbarProviderRecentSearches.sys.mjs"),
  "resource:///modules/UrlbarProviderSearchSuggestions.sys.mjs": typeof import("resource:///modules/UrlbarProviderSearchSuggestions.sys.mjs"),
  "resource:///modules/UrlbarProviderSearchTips.sys.mjs": typeof import("resource:///modules/UrlbarProviderSearchTips.sys.mjs"),
  "resource:///modules/UrlbarProviderTabToSearch.sys.mjs": typeof import("resource:///modules/UrlbarProviderTabToSearch.sys.mjs"),
  "resource:///modules/UrlbarProviderTopSites.sys.mjs": typeof import("resource:///modules/UrlbarProviderTopSites.sys.mjs"),
  "resource:///modules/UrlbarProviderUnitConversion.sys.mjs": typeof import("resource:///modules/UrlbarProviderUnitConversion.sys.mjs"),
  "resource:///modules/UrlbarProvidersManager.sys.mjs": typeof import("resource:///modules/UrlbarProvidersManager.sys.mjs"),
  "resource:///modules/UrlbarResult.sys.mjs": typeof import("resource:///modules/UrlbarResult.sys.mjs"),
  "resource:///modules/UrlbarSearchOneOffs.sys.mjs": typeof import("resource:///modules/UrlbarSearchOneOffs.sys.mjs"),
  "resource:///modules/UrlbarSearchTermsPersistence.sys.mjs": typeof import("resource:///modules/UrlbarSearchTermsPersistence.sys.mjs"),
  "resource:///modules/UrlbarSearchUtils.sys.mjs": typeof import("resource:///modules/UrlbarSearchUtils.sys.mjs"),
  "resource:///modules/UrlbarTokenizer.sys.mjs": typeof import("resource:///modules/UrlbarTokenizer.sys.mjs"),
  "resource:///modules/UrlbarUtils.sys.mjs": typeof import("resource:///modules/UrlbarUtils.sys.mjs"),
  "resource:///modules/UrlbarValueFormatter.sys.mjs": typeof import("resource:///modules/UrlbarValueFormatter.sys.mjs"),
  "resource:///modules/UrlbarView.sys.mjs": typeof import("resource:///modules/UrlbarView.sys.mjs"),
  "resource:///modules/WebProtocolHandlerRegistrar.sys.mjs": typeof import("resource:///modules/WebProtocolHandlerRegistrar.sys.mjs"),
  "resource:///modules/WindowsJumpLists.sys.mjs": typeof import("resource:///modules/WindowsJumpLists.sys.mjs"),
  "resource:///modules/ZoomUI.sys.mjs": typeof import("resource:///modules/ZoomUI.sys.mjs"),
  "resource:///modules/aboutwelcome/AWScreenUtils.sys.mjs": typeof import("resource:///modules/aboutwelcome/AWScreenUtils.sys.mjs"),
  "resource:///modules/aboutwelcome/AWToolbarUtils.sys.mjs": typeof import("resource:///modules/aboutwelcome/AWToolbarUtils.sys.mjs"),
  "resource:///modules/aboutwelcome/AboutWelcomeDefaults.sys.mjs": typeof import("resource:///modules/aboutwelcome/AboutWelcomeDefaults.sys.mjs"),
  "resource:///modules/aboutwelcome/AboutWelcomeTelemetry.sys.mjs": typeof import("resource:///modules/aboutwelcome/AboutWelcomeTelemetry.sys.mjs"),
  "resource:///modules/asrouter/ASRouter.sys.mjs": typeof import("resource:///modules/asrouter/ASRouter.sys.mjs"),
  "resource:///modules/asrouter/ASRouterDefaultConfig.sys.mjs": typeof import("resource:///modules/asrouter/ASRouterDefaultConfig.sys.mjs"),
  "resource:///modules/asrouter/ASRouterNewTabHook.sys.mjs": typeof import("resource:///modules/asrouter/ASRouterNewTabHook.sys.mjs"),
  "resource:///modules/asrouter/ASRouterPreferences.sys.mjs": typeof import("resource:///modules/asrouter/ASRouterPreferences.sys.mjs"),
  "resource:///modules/asrouter/ASRouterStorage.sys.mjs": typeof import("resource:///modules/asrouter/ASRouterStorage.sys.mjs"),
  "resource:///modules/asrouter/ASRouterTargeting.sys.mjs": typeof import("resource:///modules/asrouter/ASRouterTargeting.sys.mjs"),
  "resource:///modules/asrouter/ASRouterTriggerListeners.sys.mjs": typeof import("resource:///modules/asrouter/ASRouterTriggerListeners.sys.mjs"),
  "resource:///modules/asrouter/ActorConstants.mjs": typeof import("resource:///modules/asrouter/ActorConstants.mjs"),
  "resource:///modules/asrouter/BookmarksBarButton.sys.mjs": typeof import("resource:///modules/asrouter/BookmarksBarButton.sys.mjs"),
  "resource:///modules/asrouter/CFRMessageProvider.sys.mjs": typeof import("resource:///modules/asrouter/CFRMessageProvider.sys.mjs"),
  "resource:///modules/asrouter/CFRPageActions.sys.mjs": typeof import("resource:///modules/asrouter/CFRPageActions.sys.mjs"),
  "resource:///modules/asrouter/FeatureCallout.sys.mjs": typeof import("resource:///modules/asrouter/FeatureCallout.sys.mjs"),
  "resource:///modules/asrouter/FeatureCalloutBroker.sys.mjs": typeof import("resource:///modules/asrouter/FeatureCalloutBroker.sys.mjs"),
  "resource:///modules/asrouter/FeatureCalloutMessages.sys.mjs": typeof import("resource:///modules/asrouter/FeatureCalloutMessages.sys.mjs"),
  "resource:///modules/asrouter/InfoBar.sys.mjs": typeof import("resource:///modules/asrouter/InfoBar.sys.mjs"),
  "resource:///modules/asrouter/MenuMessage.sys.mjs": typeof import("resource:///modules/asrouter/MenuMessage.sys.mjs"),
  "resource:///modules/asrouter/MomentsPageHub.sys.mjs": typeof import("resource:///modules/asrouter/MomentsPageHub.sys.mjs"),
  "resource:///modules/asrouter/OnboardingMessageProvider.sys.mjs": typeof import("resource:///modules/asrouter/OnboardingMessageProvider.sys.mjs"),
  "resource:///modules/asrouter/PageEventManager.sys.mjs": typeof import("resource:///modules/asrouter/PageEventManager.sys.mjs"),
  "resource:///modules/asrouter/PanelTestProvider.sys.mjs": typeof import("resource:///modules/asrouter/PanelTestProvider.sys.mjs"),
  "resource:///modules/asrouter/RemoteL10n.sys.mjs": typeof import("resource:///modules/asrouter/RemoteL10n.sys.mjs"),
  "resource:///modules/asrouter/Spotlight.sys.mjs": typeof import("resource:///modules/asrouter/Spotlight.sys.mjs"),
  "resource:///modules/asrouter/ToastNotification.sys.mjs": typeof import("resource:///modules/asrouter/ToastNotification.sys.mjs"),
  "resource:///modules/asrouter/ToolbarBadgeHub.sys.mjs": typeof import("resource:///modules/asrouter/ToolbarBadgeHub.sys.mjs"),
  "resource:///modules/backup/ArchiveEncryption.sys.mjs": typeof import("resource:///modules/backup/ArchiveEncryption.sys.mjs"),
  "resource:///modules/backup/ArchiveEncryptionState.sys.mjs": typeof import("resource:///modules/backup/ArchiveEncryptionState.sys.mjs"),
  "resource:///modules/backup/ArchiveUtils.sys.mjs": typeof import("resource:///modules/backup/ArchiveUtils.sys.mjs"),
  "resource:///modules/backup/BackupError.mjs": typeof import("resource:///modules/backup/BackupError.mjs"),
  "resource:///modules/backup/BackupService.sys.mjs": typeof import("resource:///modules/backup/BackupService.sys.mjs"),
  "resource:///modules/distribution.sys.mjs": typeof import("resource:///modules/distribution.sys.mjs"),
  "resource:///modules/firefox-view-synced-tabs-error-handler.sys.mjs": typeof import("resource:///modules/firefox-view-synced-tabs-error-handler.sys.mjs"),
  "resource:///modules/firefox-view-tabs-setup-manager.sys.mjs": typeof import("resource:///modules/firefox-view-tabs-setup-manager.sys.mjs"),
  "resource:///modules/pagedata/OpenGraphPageData.sys.mjs": typeof import("resource:///modules/pagedata/OpenGraphPageData.sys.mjs"),
  "resource:///modules/pagedata/PageDataSchema.sys.mjs": typeof import("resource:///modules/pagedata/PageDataSchema.sys.mjs"),
  "resource:///modules/pagedata/PageDataService.sys.mjs": typeof import("resource:///modules/pagedata/PageDataService.sys.mjs"),
  "resource:///modules/pagedata/SchemaOrgPageData.sys.mjs": typeof import("resource:///modules/pagedata/SchemaOrgPageData.sys.mjs"),
  "resource:///modules/pagedata/TwitterPageData.sys.mjs": typeof import("resource:///modules/pagedata/TwitterPageData.sys.mjs"),
  "resource:///modules/policies/BookmarksPolicies.sys.mjs": typeof import("resource:///modules/policies/BookmarksPolicies.sys.mjs"),
  "resource:///modules/policies/Policies.sys.mjs": typeof import("resource:///modules/policies/Policies.sys.mjs"),
  "resource:///modules/policies/ProxyPolicies.sys.mjs": typeof import("resource:///modules/policies/ProxyPolicies.sys.mjs"),
  "resource:///modules/policies/WebsiteFilter.sys.mjs": typeof import("resource:///modules/policies/WebsiteFilter.sys.mjs"),
  "resource:///modules/policies/schema.sys.mjs": typeof import("resource:///modules/policies/schema.sys.mjs"),
  "resource:///modules/profiles/SelectableProfileService.sys.mjs": typeof import("resource:///modules/profiles/SelectableProfileService.sys.mjs"),
  "resource:///modules/sessionstore/PageWireframes.sys.mjs": typeof import("resource:///modules/sessionstore/PageWireframes.sys.mjs"),
  "resource:///modules/sessionstore/RecentlyClosedTabsAndWindowsMenuUtils.sys.mjs": typeof import("resource:///modules/sessionstore/RecentlyClosedTabsAndWindowsMenuUtils.sys.mjs"),
  "resource:///modules/sessionstore/RunState.sys.mjs": typeof import("resource:///modules/sessionstore/RunState.sys.mjs"),
  "resource:///modules/sessionstore/SessionCookies.sys.mjs": typeof import("resource:///modules/sessionstore/SessionCookies.sys.mjs"),
  "resource:///modules/sessionstore/SessionFile.sys.mjs": typeof import("resource:///modules/sessionstore/SessionFile.sys.mjs"),
  "resource:///modules/sessionstore/SessionLogger.sys.mjs": typeof import("resource:///modules/sessionstore/SessionLogger.sys.mjs"),
  "resource:///modules/sessionstore/SessionMigration.sys.mjs": typeof import("resource:///modules/sessionstore/SessionMigration.sys.mjs"),
  "resource:///modules/sessionstore/SessionSaver.sys.mjs": typeof import("resource:///modules/sessionstore/SessionSaver.sys.mjs"),
  "resource:///modules/sessionstore/SessionStartup.sys.mjs": typeof import("resource:///modules/sessionstore/SessionStartup.sys.mjs"),
  "resource:///modules/sessionstore/SessionStore.sys.mjs": typeof import("resource:///modules/sessionstore/SessionStore.sys.mjs"),
  "resource:///modules/sessionstore/SessionWriter.sys.mjs": typeof import("resource:///modules/sessionstore/SessionWriter.sys.mjs"),
  "resource:///modules/sessionstore/StartupPerformance.sys.mjs": typeof import("resource:///modules/sessionstore/StartupPerformance.sys.mjs"),
  "resource:///modules/sessionstore/TabAttributes.sys.mjs": typeof import("resource:///modules/sessionstore/TabAttributes.sys.mjs"),
  "resource:///modules/sessionstore/TabGroupState.sys.mjs": typeof import("resource:///modules/sessionstore/TabGroupState.sys.mjs"),
  "resource:///modules/sessionstore/TabState.sys.mjs": typeof import("resource:///modules/sessionstore/TabState.sys.mjs"),
  "resource:///modules/sessionstore/TabStateCache.sys.mjs": typeof import("resource:///modules/sessionstore/TabStateCache.sys.mjs"),
  "resource:///modules/sessionstore/TabStateFlusher.sys.mjs": typeof import("resource:///modules/sessionstore/TabStateFlusher.sys.mjs"),
  "resource:///modules/taskbartabs/TaskbarTabUI.sys.mjs": typeof import("resource:///modules/taskbartabs/TaskbarTabUI.sys.mjs"),
  "resource:///modules/topsites/TippyTopProvider.sys.mjs": typeof import("resource:///modules/topsites/TippyTopProvider.sys.mjs"),
  "resource:///modules/topsites/TopSites.sys.mjs": typeof import("resource:///modules/topsites/TopSites.sys.mjs"),
  "resource:///modules/topsites/constants.mjs": typeof import("resource:///modules/topsites/constants.mjs"),
  "resource:///modules/urlbar/private/AmpSuggestions.sys.mjs": typeof import("resource:///modules/urlbar/private/AmpSuggestions.sys.mjs"),
  "resource:///modules/urlbar/private/GeolocationUtils.sys.mjs": typeof import("resource:///modules/urlbar/private/GeolocationUtils.sys.mjs"),
  "resource:///modules/urlbar/private/MLSuggest.sys.mjs": typeof import("resource:///modules/urlbar/private/MLSuggest.sys.mjs"),
  "resource:///modules/webrtcUI.sys.mjs": typeof import("resource:///modules/webrtcUI.sys.mjs"),
  "resource://autofill/FormAutofill.sys.mjs": typeof import("resource://autofill/FormAutofill.sys.mjs"),
  "resource://autofill/FormAutofillContent.sys.mjs": typeof import("resource://autofill/FormAutofillContent.sys.mjs"),
  "resource://autofill/FormAutofillParent.sys.mjs": typeof import("resource://autofill/FormAutofillParent.sys.mjs"),
  "resource://autofill/FormAutofillPreferences.sys.mjs": typeof import("resource://autofill/FormAutofillPreferences.sys.mjs"),
  "resource://autofill/FormAutofillPrompter.sys.mjs": typeof import("resource://autofill/FormAutofillPrompter.sys.mjs"),
  "resource://autofill/FormAutofillStorage.sys.mjs": typeof import("resource://autofill/FormAutofillStorage.sys.mjs"),
  "resource://autofill/MLAutofill.sys.mjs": typeof import("resource://autofill/MLAutofill.sys.mjs"),
  "resource://autofill/ProfileAutoCompleteResult.sys.mjs": typeof import("resource://autofill/ProfileAutoCompleteResult.sys.mjs"),
  "resource://devtools/client/framework/browser-toolbox/Launcher.sys.mjs": typeof import("resource://devtools/client/framework/browser-toolbox/Launcher.sys.mjs"),
  "resource://devtools/client/performance-new/popup/menu-button.sys.mjs": typeof import("resource://devtools/client/performance-new/popup/menu-button.sys.mjs"),
  "resource://devtools/client/shared/components/reps/reps/constants.mjs": typeof import("resource://devtools/client/shared/components/reps/reps/constants.mjs"),
  "resource://devtools/client/shared/components/reps/reps/rep-utils.mjs": typeof import("resource://devtools/client/shared/components/reps/reps/rep-utils.mjs"),
  "resource://devtools/client/shared/focus.mjs": typeof import("resource://devtools/client/shared/focus.mjs"),
  "resource://devtools/client/storage/VariablesView.sys.mjs": typeof import("resource://devtools/client/storage/VariablesView.sys.mjs"),
  "resource://devtools/server/actors/targets/target-actor-registry.sys.mjs": typeof import("resource://devtools/server/actors/targets/target-actor-registry.sys.mjs"),
  "resource://devtools/server/actors/watcher/SessionDataHelpers.sys.mjs": typeof import("resource://devtools/server/actors/watcher/SessionDataHelpers.sys.mjs"),
  "resource://devtools/server/actors/watcher/browsing-context-helpers.sys.mjs": typeof import("resource://devtools/server/actors/watcher/browsing-context-helpers.sys.mjs"),
  "resource://devtools/server/connectors/js-process-actor/target-watchers/content_script.sys.mjs": typeof import("resource://devtools/server/connectors/js-process-actor/target-watchers/content_script.sys.mjs"),
  "resource://devtools/server/connectors/js-process-actor/target-watchers/process.sys.mjs": typeof import("resource://devtools/server/connectors/js-process-actor/target-watchers/process.sys.mjs"),
  "resource://devtools/server/connectors/js-process-actor/target-watchers/service_worker.sys.mjs": typeof import("resource://devtools/server/connectors/js-process-actor/target-watchers/service_worker.sys.mjs"),
  "resource://devtools/server/connectors/js-process-actor/target-watchers/shared_worker.sys.mjs": typeof import("resource://devtools/server/connectors/js-process-actor/target-watchers/shared_worker.sys.mjs"),
  "resource://devtools/server/connectors/js-process-actor/target-watchers/window-global.sys.mjs": typeof import("resource://devtools/server/connectors/js-process-actor/target-watchers/window-global.sys.mjs"),
  "resource://devtools/server/connectors/js-process-actor/target-watchers/worker.sys.mjs": typeof import("resource://devtools/server/connectors/js-process-actor/target-watchers/worker.sys.mjs"),
  "resource://devtools/server/tracer/tracer.sys.mjs": typeof import("resource://devtools/server/tracer/tracer.sys.mjs"),
  "resource://devtools/shared/DevToolsInfaillibleUtils.sys.mjs": typeof import("resource://devtools/shared/DevToolsInfaillibleUtils.sys.mjs"),
  "resource://devtools/shared/highlighters.mjs": typeof import("resource://devtools/shared/highlighters.mjs"),
  "resource://devtools/shared/loader/DistinctSystemPrincipalLoader.sys.mjs": typeof import("resource://devtools/shared/loader/DistinctSystemPrincipalLoader.sys.mjs"),
  "resource://devtools/shared/loader/Loader.sys.mjs": typeof import("resource://devtools/shared/loader/Loader.sys.mjs"),
  "resource://devtools/shared/network-observer/ChannelMap.sys.mjs": typeof import("resource://devtools/shared/network-observer/ChannelMap.sys.mjs"),
  "resource://devtools/shared/network-observer/NetworkAuthListener.sys.mjs": typeof import("resource://devtools/shared/network-observer/NetworkAuthListener.sys.mjs"),
  "resource://devtools/shared/network-observer/NetworkHelper.sys.mjs": typeof import("resource://devtools/shared/network-observer/NetworkHelper.sys.mjs"),
  "resource://devtools/shared/network-observer/NetworkObserver.sys.mjs": typeof import("resource://devtools/shared/network-observer/NetworkObserver.sys.mjs"),
  "resource://devtools/shared/network-observer/NetworkOverride.sys.mjs": typeof import("resource://devtools/shared/network-observer/NetworkOverride.sys.mjs"),
  "resource://devtools/shared/network-observer/NetworkResponseListener.sys.mjs": typeof import("resource://devtools/shared/network-observer/NetworkResponseListener.sys.mjs"),
  "resource://devtools/shared/network-observer/NetworkThrottleManager.sys.mjs": typeof import("resource://devtools/shared/network-observer/NetworkThrottleManager.sys.mjs"),
  "resource://devtools/shared/network-observer/NetworkTimings.sys.mjs": typeof import("resource://devtools/shared/network-observer/NetworkTimings.sys.mjs"),
  "resource://devtools/shared/network-observer/NetworkUtils.sys.mjs": typeof import("resource://devtools/shared/network-observer/NetworkUtils.sys.mjs"),
  "resource://devtools/shared/network-observer/WildcardToRegexp.sys.mjs": typeof import("resource://devtools/shared/network-observer/WildcardToRegexp.sys.mjs"),
  "resource://devtools/shared/performance-new/recording-utils.sys.mjs": typeof import("resource://devtools/shared/performance-new/recording-utils.sys.mjs"),
  "resource://devtools/shared/platform/CacheEntry.sys.mjs": typeof import("resource://devtools/shared/platform/CacheEntry.sys.mjs"),
  "resource://devtools/shared/security/DevToolsSocketStatus.sys.mjs": typeof import("resource://devtools/shared/security/DevToolsSocketStatus.sys.mjs"),
  "resource://devtools/shared/validate-breakpoint.sys.mjs": typeof import("resource://devtools/shared/validate-breakpoint.sys.mjs"),
  "resource://devtools/shared/worker/worker.sys.mjs": typeof import("resource://devtools/shared/worker/worker.sys.mjs"),
  "resource://gre/actors/AutoCompleteParent.sys.mjs": typeof import("resource://gre/actors/AutoCompleteParent.sys.mjs"),
  "resource://gre/actors/FormHandlerChild.sys.mjs": typeof import("resource://gre/actors/FormHandlerChild.sys.mjs"),
  "resource://gre/actors/MLEngineParent.sys.mjs": typeof import("resource://gre/actors/MLEngineParent.sys.mjs"),
  "resource://gre/actors/PopupBlockingParent.sys.mjs": typeof import("resource://gre/actors/PopupBlockingParent.sys.mjs"),
  "resource://gre/actors/SelectParent.sys.mjs": typeof import("resource://gre/actors/SelectParent.sys.mjs"),
  "resource://gre/actors/TranslationsParent.sys.mjs": typeof import("resource://gre/actors/TranslationsParent.sys.mjs"),
  "resource://gre/actors/ViewSourcePageChild.sys.mjs": typeof import("resource://gre/actors/ViewSourcePageChild.sys.mjs"),
  "resource://gre/modules/AboutPagesUtils.sys.mjs": typeof import("resource://gre/modules/AboutPagesUtils.sys.mjs"),
  "resource://gre/modules/AbuseReporter.sys.mjs": typeof import("resource://gre/modules/AbuseReporter.sys.mjs"),
  "resource://gre/modules/ActorManagerParent.sys.mjs": typeof import("resource://gre/modules/ActorManagerParent.sys.mjs"),
  "resource://gre/modules/AddonManager.sys.mjs": typeof import("resource://gre/modules/AddonManager.sys.mjs"),
  "resource://gre/modules/AndroidLog.sys.mjs": typeof import("resource://gre/modules/AndroidLog.sys.mjs"),
  "resource://gre/modules/AppConstants.sys.mjs": typeof import("resource://gre/modules/AppConstants.sys.mjs"),
  "resource://gre/modules/AppMenuNotifications.sys.mjs": typeof import("resource://gre/modules/AppMenuNotifications.sys.mjs"),
  "resource://gre/modules/AppUpdater.sys.mjs": typeof import("resource://gre/modules/AppUpdater.sys.mjs"),
  "resource://gre/modules/AsyncPrefs.sys.mjs": typeof import("resource://gre/modules/AsyncPrefs.sys.mjs"),
  "resource://gre/modules/AsyncShutdown.sys.mjs": typeof import("resource://gre/modules/AsyncShutdown.sys.mjs"),
  "resource://gre/modules/BackgroundPageThumbs.sys.mjs": typeof import("resource://gre/modules/BackgroundPageThumbs.sys.mjs"),
  "resource://gre/modules/BackgroundTasksUtils.sys.mjs": typeof import("resource://gre/modules/BackgroundTasksUtils.sys.mjs"),
  "resource://gre/modules/BackgroundUpdate.sys.mjs": typeof import("resource://gre/modules/BackgroundUpdate.sys.mjs"),
  "resource://gre/modules/BinarySearch.sys.mjs": typeof import("resource://gre/modules/BinarySearch.sys.mjs"),
  "resource://gre/modules/Blocklist.sys.mjs": typeof import("resource://gre/modules/Blocklist.sys.mjs"),
  "resource://gre/modules/BookmarkHTMLUtils.sys.mjs": typeof import("resource://gre/modules/BookmarkHTMLUtils.sys.mjs"),
  "resource://gre/modules/BookmarkJSONUtils.sys.mjs": typeof import("resource://gre/modules/BookmarkJSONUtils.sys.mjs"),
  "resource://gre/modules/BookmarkList.sys.mjs": typeof import("resource://gre/modules/BookmarkList.sys.mjs"),
  "resource://gre/modules/Bookmarks.sys.mjs": typeof import("resource://gre/modules/Bookmarks.sys.mjs"),
  "resource://gre/modules/BrowserTelemetryUtils.sys.mjs": typeof import("resource://gre/modules/BrowserTelemetryUtils.sys.mjs"),
  "resource://gre/modules/BrowserUtils.sys.mjs": typeof import("resource://gre/modules/BrowserUtils.sys.mjs"),
  "resource://gre/modules/CSV.sys.mjs": typeof import("resource://gre/modules/CSV.sys.mjs"),
  "resource://gre/modules/CanonicalJSON.sys.mjs": typeof import("resource://gre/modules/CanonicalJSON.sys.mjs"),
  "resource://gre/modules/CaptchaDetectionPingUtils.sys.mjs": typeof import("resource://gre/modules/CaptchaDetectionPingUtils.sys.mjs"),
  "resource://gre/modules/CaptchaResponseObserver.sys.mjs": typeof import("resource://gre/modules/CaptchaResponseObserver.sys.mjs"),
  "resource://gre/modules/CertUtils.sys.mjs": typeof import("resource://gre/modules/CertUtils.sys.mjs"),
  "resource://gre/modules/ChildCrashHandler.sys.mjs": typeof import("resource://gre/modules/ChildCrashHandler.sys.mjs"),
  "resource://gre/modules/ClientID.sys.mjs": typeof import("resource://gre/modules/ClientID.sys.mjs"),
  "resource://gre/modules/ClipboardContextMenu.sys.mjs": typeof import("resource://gre/modules/ClipboardContextMenu.sys.mjs"),
  "resource://gre/modules/Color.sys.mjs": typeof import("resource://gre/modules/Color.sys.mjs"),
  "resource://gre/modules/ColorwayThemeMigration.sys.mjs": typeof import("resource://gre/modules/ColorwayThemeMigration.sys.mjs"),
  "resource://gre/modules/CommonDialog.sys.mjs": typeof import("resource://gre/modules/CommonDialog.sys.mjs"),
  "resource://gre/modules/ConduitsParent.sys.mjs": typeof import("resource://gre/modules/ConduitsParent.sys.mjs"),
  "resource://gre/modules/Console.sys.mjs": typeof import("resource://gre/modules/Console.sys.mjs"),
  "resource://gre/modules/ContentAnalysisUtils.sys.mjs": typeof import("resource://gre/modules/ContentAnalysisUtils.sys.mjs"),
  "resource://gre/modules/ContentBlockingAllowList.sys.mjs": typeof import("resource://gre/modules/ContentBlockingAllowList.sys.mjs"),
  "resource://gre/modules/ContentDOMReference.sys.mjs": typeof import("resource://gre/modules/ContentDOMReference.sys.mjs"),
  "resource://gre/modules/ContentPrefUtils.sys.mjs": typeof import("resource://gre/modules/ContentPrefUtils.sys.mjs"),
  "resource://gre/modules/ContentRelevancyManager.sys.mjs": typeof import("resource://gre/modules/ContentRelevancyManager.sys.mjs"),
  "resource://gre/modules/ContextualIdentityService.sys.mjs": typeof import("resource://gre/modules/ContextualIdentityService.sys.mjs"),
  "resource://gre/modules/CoveragePing.sys.mjs": typeof import("resource://gre/modules/CoveragePing.sys.mjs"),
  "resource://gre/modules/CrashMonitor.sys.mjs": typeof import("resource://gre/modules/CrashMonitor.sys.mjs"),
  "resource://gre/modules/CrashService.sys.mjs": typeof import("resource://gre/modules/CrashService.sys.mjs"),
  "resource://gre/modules/CrashSubmit.sys.mjs": typeof import("resource://gre/modules/CrashSubmit.sys.mjs"),
  "resource://gre/modules/CreditCard.sys.mjs": typeof import("resource://gre/modules/CreditCard.sys.mjs"),
  "resource://gre/modules/DAPTelemetrySender.sys.mjs": typeof import("resource://gre/modules/DAPTelemetrySender.sys.mjs"),
  "resource://gre/modules/DAPVisitCounter.sys.mjs": typeof import("resource://gre/modules/DAPVisitCounter.sys.mjs"),
  "resource://gre/modules/DateTimePickerPanel.sys.mjs": typeof import("resource://gre/modules/DateTimePickerPanel.sys.mjs"),
  "resource://gre/modules/DeferredTask.sys.mjs": typeof import("resource://gre/modules/DeferredTask.sys.mjs"),
  "moz-src:///toolkit/components/doh/DoHConfig.sys.mjs": typeof import("moz-src:///toolkit/components/doh/DoHConfig.sys.mjs"),
  "moz-src:///toolkit/components/doh/DoHController.sys.mjs": typeof import("moz-src:///toolkit/components/doh/DoHController.sys.mjs"),
  "moz-src:///toolkit/components/doh/DoHHeuristics.sys.mjs": typeof import("moz-src:///toolkit/components/doh/DoHHeuristics.sys.mjs"),
  "resource://gre/modules/DownloadCore.sys.mjs": typeof import("resource://gre/modules/DownloadCore.sys.mjs"),
  "resource://gre/modules/DownloadHistory.sys.mjs": typeof import("resource://gre/modules/DownloadHistory.sys.mjs"),
  "resource://gre/modules/DownloadIntegration.sys.mjs": typeof import("resource://gre/modules/DownloadIntegration.sys.mjs"),
  "resource://gre/modules/DownloadLastDir.sys.mjs": typeof import("resource://gre/modules/DownloadLastDir.sys.mjs"),
  "resource://gre/modules/DownloadList.sys.mjs": typeof import("resource://gre/modules/DownloadList.sys.mjs"),
  "resource://gre/modules/DownloadPaths.sys.mjs": typeof import("resource://gre/modules/DownloadPaths.sys.mjs"),
  "resource://gre/modules/DownloadStore.sys.mjs": typeof import("resource://gre/modules/DownloadStore.sys.mjs"),
  "resource://gre/modules/DownloadUIHelper.sys.mjs": typeof import("resource://gre/modules/DownloadUIHelper.sys.mjs"),
  "resource://gre/modules/DownloadUtils.sys.mjs": typeof import("resource://gre/modules/DownloadUtils.sys.mjs"),
  "resource://gre/modules/Downloads.sys.mjs": typeof import("resource://gre/modules/Downloads.sys.mjs"),
  "resource://gre/modules/E10SUtils.sys.mjs": typeof import("resource://gre/modules/E10SUtils.sys.mjs"),
  "resource://gre/modules/EssentialDomainsRemoteSettings.sys.mjs": typeof import("resource://gre/modules/EssentialDomainsRemoteSettings.sys.mjs"),
  "resource://gre/modules/EventEmitter.sys.mjs": typeof import("resource://gre/modules/EventEmitter.sys.mjs"),
  "resource://gre/modules/EventPing.sys.mjs": typeof import("resource://gre/modules/EventPing.sys.mjs"),
  "resource://gre/modules/Extension.sys.mjs": typeof import("resource://gre/modules/Extension.sys.mjs"),
  "resource://gre/modules/ExtensionActivityLog.sys.mjs": typeof import("resource://gre/modules/ExtensionActivityLog.sys.mjs"),
  "resource://gre/modules/ExtensionChild.sys.mjs": typeof import("resource://gre/modules/ExtensionChild.sys.mjs"),
  "resource://gre/modules/ExtensionChildDevToolsUtils.sys.mjs": typeof import("resource://gre/modules/ExtensionChildDevToolsUtils.sys.mjs"),
  "resource://gre/modules/ExtensionCommon.sys.mjs": typeof import("resource://gre/modules/ExtensionCommon.sys.mjs"),
  "resource://gre/modules/ExtensionContent.sys.mjs": typeof import("resource://gre/modules/ExtensionContent.sys.mjs"),
  "resource://gre/modules/ExtensionDNR.sys.mjs": typeof import("resource://gre/modules/ExtensionDNR.sys.mjs"),
  "resource://gre/modules/ExtensionDNRLimits.sys.mjs": typeof import("resource://gre/modules/ExtensionDNRLimits.sys.mjs"),
  "resource://gre/modules/ExtensionDNRStore.sys.mjs": typeof import("resource://gre/modules/ExtensionDNRStore.sys.mjs"),
  "resource://gre/modules/ExtensionMenus.sys.mjs": typeof import("resource://gre/modules/ExtensionMenus.sys.mjs"),
  "resource://gre/modules/ExtensionPageChild.sys.mjs": typeof import("resource://gre/modules/ExtensionPageChild.sys.mjs"),
  "resource://gre/modules/ExtensionParent.sys.mjs": typeof import("resource://gre/modules/ExtensionParent.sys.mjs"),
  "resource://gre/modules/ExtensionPermissionMessages.sys.mjs": typeof import("resource://gre/modules/ExtensionPermissionMessages.sys.mjs"),
  "resource://gre/modules/ExtensionPermissions.sys.mjs": typeof import("resource://gre/modules/ExtensionPermissions.sys.mjs"),
  "resource://gre/modules/ExtensionPreferencesManager.sys.mjs": typeof import("resource://gre/modules/ExtensionPreferencesManager.sys.mjs"),
  "resource://gre/modules/ExtensionProcessScript.sys.mjs": typeof import("resource://gre/modules/ExtensionProcessScript.sys.mjs"),
  "resource://gre/modules/ExtensionScriptingStore.sys.mjs": typeof import("resource://gre/modules/ExtensionScriptingStore.sys.mjs"),
  "resource://gre/modules/ExtensionSearchHandler.sys.mjs": typeof import("resource://gre/modules/ExtensionSearchHandler.sys.mjs"),
  "resource://gre/modules/ExtensionSettingsStore.sys.mjs": typeof import("resource://gre/modules/ExtensionSettingsStore.sys.mjs"),
  "resource://gre/modules/ExtensionShortcuts.sys.mjs": typeof import("resource://gre/modules/ExtensionShortcuts.sys.mjs"),
  "resource://gre/modules/ExtensionStorage.sys.mjs": typeof import("resource://gre/modules/ExtensionStorage.sys.mjs"),
  "resource://gre/modules/ExtensionStorageComponents.sys.mjs": typeof import("resource://gre/modules/ExtensionStorageComponents.sys.mjs"),
  "resource://gre/modules/ExtensionStorageIDB.sys.mjs": typeof import("resource://gre/modules/ExtensionStorageIDB.sys.mjs"),
  "resource://gre/modules/ExtensionStorageSync.sys.mjs": typeof import("resource://gre/modules/ExtensionStorageSync.sys.mjs"),
  "resource://gre/modules/ExtensionStorageSyncKinto.sys.mjs": typeof import("resource://gre/modules/ExtensionStorageSyncKinto.sys.mjs"),
  "resource://gre/modules/ExtensionTelemetry.sys.mjs": typeof import("resource://gre/modules/ExtensionTelemetry.sys.mjs"),
  "resource://gre/modules/ExtensionUserScripts.sys.mjs": typeof import("resource://gre/modules/ExtensionUserScripts.sys.mjs"),
  "resource://gre/modules/ExtensionUserScriptsContent.sys.mjs": typeof import("resource://gre/modules/ExtensionUserScriptsContent.sys.mjs"),
  "resource://gre/modules/ExtensionUtils.sys.mjs": typeof import("resource://gre/modules/ExtensionUtils.sys.mjs"),
  "resource://gre/modules/ExtensionWorkerChild.sys.mjs": typeof import("resource://gre/modules/ExtensionWorkerChild.sys.mjs"),
  "resource://gre/modules/FileUtils.sys.mjs": typeof import("resource://gre/modules/FileUtils.sys.mjs"),
  "resource://gre/modules/FillHelpers.sys.mjs": typeof import("resource://gre/modules/FillHelpers.sys.mjs"),
  "resource://gre/modules/FindContent.sys.mjs": typeof import("resource://gre/modules/FindContent.sys.mjs"),
  "resource://gre/modules/Finder.sys.mjs": typeof import("resource://gre/modules/Finder.sys.mjs"),
  "resource://gre/modules/FinderHighlighter.sys.mjs": typeof import("resource://gre/modules/FinderHighlighter.sys.mjs"),
  "resource://gre/modules/FinderIterator.sys.mjs": typeof import("resource://gre/modules/FinderIterator.sys.mjs"),
  "resource://gre/modules/FinderParent.sys.mjs": typeof import("resource://gre/modules/FinderParent.sys.mjs"),
  "resource://gre/modules/FirefoxRelay.sys.mjs": typeof import("resource://gre/modules/FirefoxRelay.sys.mjs"),
  "resource://gre/modules/FirstStartup.sys.mjs": typeof import("resource://gre/modules/FirstStartup.sys.mjs"),
  "resource://gre/modules/ForgetAboutSite.sys.mjs": typeof import("resource://gre/modules/ForgetAboutSite.sys.mjs"),
  "resource://gre/modules/FormHistory.sys.mjs": typeof import("resource://gre/modules/FormHistory.sys.mjs"),
  "resource://gre/modules/FormHistoryAutoComplete.sys.mjs": typeof import("resource://gre/modules/FormHistoryAutoComplete.sys.mjs"),
  "resource://gre/modules/FormLikeFactory.sys.mjs": typeof import("resource://gre/modules/FormLikeFactory.sys.mjs"),
  "resource://gre/modules/FormScenarios.sys.mjs": typeof import("resource://gre/modules/FormScenarios.sys.mjs"),
  "resource://gre/modules/FxAccounts.sys.mjs": typeof import("resource://gre/modules/FxAccounts.sys.mjs"),
  "resource://gre/modules/FxAccountsClient.sys.mjs": typeof import("resource://gre/modules/FxAccountsClient.sys.mjs"),
  "resource://gre/modules/FxAccountsCommands.sys.mjs": typeof import("resource://gre/modules/FxAccountsCommands.sys.mjs"),
  "resource://gre/modules/FxAccountsCommon.sys.mjs": typeof import("resource://gre/modules/FxAccountsCommon.sys.mjs"),
  "resource://gre/modules/FxAccountsConfig.sys.mjs": typeof import("resource://gre/modules/FxAccountsConfig.sys.mjs"),
  "resource://gre/modules/FxAccountsDevice.sys.mjs": typeof import("resource://gre/modules/FxAccountsDevice.sys.mjs"),
  "resource://gre/modules/FxAccountsKeys.sys.mjs": typeof import("resource://gre/modules/FxAccountsKeys.sys.mjs"),
  "resource://gre/modules/FxAccountsOAuth.sys.mjs": typeof import("resource://gre/modules/FxAccountsOAuth.sys.mjs"),
  "resource://gre/modules/FxAccountsPairing.sys.mjs": typeof import("resource://gre/modules/FxAccountsPairing.sys.mjs"),
  "resource://gre/modules/FxAccountsPairingChannel.sys.mjs": typeof import("resource://gre/modules/FxAccountsPairingChannel.sys.mjs"),
  "resource://gre/modules/FxAccountsProfile.sys.mjs": typeof import("resource://gre/modules/FxAccountsProfile.sys.mjs"),
  "resource://gre/modules/FxAccountsProfileClient.sys.mjs": typeof import("resource://gre/modules/FxAccountsProfileClient.sys.mjs"),
  "resource://gre/modules/FxAccountsStorage.sys.mjs": typeof import("resource://gre/modules/FxAccountsStorage.sys.mjs"),
  "resource://gre/modules/FxAccountsTelemetry.sys.mjs": typeof import("resource://gre/modules/FxAccountsTelemetry.sys.mjs"),
  "resource://gre/modules/FxAccountsWebChannel.sys.mjs": typeof import("resource://gre/modules/FxAccountsWebChannel.sys.mjs"),
  "resource://gre/modules/GMPInstallManager.sys.mjs": typeof import("resource://gre/modules/GMPInstallManager.sys.mjs"),
  "resource://gre/modules/GeckoViewActorManager.sys.mjs": typeof import("resource://gre/modules/GeckoViewActorManager.sys.mjs"),
  "resource://gre/modules/GeckoViewAutocomplete.sys.mjs": typeof import("resource://gre/modules/GeckoViewAutocomplete.sys.mjs"),
  "resource://gre/modules/GeckoViewAutofill.sys.mjs": typeof import("resource://gre/modules/GeckoViewAutofill.sys.mjs"),
  "resource://gre/modules/GeckoViewClipboardPermission.sys.mjs": typeof import("resource://gre/modules/GeckoViewClipboardPermission.sys.mjs"),
  "resource://gre/modules/GeckoViewIdentityCredential.sys.mjs": typeof import("resource://gre/modules/GeckoViewIdentityCredential.sys.mjs"),
  "resource://gre/modules/GeckoViewPrompter.sys.mjs": typeof import("resource://gre/modules/GeckoViewPrompter.sys.mjs"),
  "resource://gre/modules/GeckoViewSettings.sys.mjs": typeof import("resource://gre/modules/GeckoViewSettings.sys.mjs"),
  "resource://gre/modules/GeckoViewTab.sys.mjs": typeof import("resource://gre/modules/GeckoViewTab.sys.mjs"),
  "resource://gre/modules/GeckoViewTelemetry.sys.mjs": typeof import("resource://gre/modules/GeckoViewTelemetry.sys.mjs"),
  "resource://gre/modules/GeckoViewTestUtils.sys.mjs": typeof import("resource://gre/modules/GeckoViewTestUtils.sys.mjs"),
  "resource://gre/modules/GeckoViewUtils.sys.mjs": typeof import("resource://gre/modules/GeckoViewUtils.sys.mjs"),
  "resource://gre/modules/GeckoViewWebExtension.sys.mjs": typeof import("resource://gre/modules/GeckoViewWebExtension.sys.mjs"),
  "resource://gre/modules/Geometry.sys.mjs": typeof import("resource://gre/modules/Geometry.sys.mjs"),
  "resource://gre/modules/HPKEConfigManager.sys.mjs": typeof import("resource://gre/modules/HPKEConfigManager.sys.mjs"),
  "resource://gre/modules/HealthPing.sys.mjs": typeof import("resource://gre/modules/HealthPing.sys.mjs"),
  "resource://gre/modules/HiddenFrame.sys.mjs": typeof import("resource://gre/modules/HiddenFrame.sys.mjs"),
  "resource://gre/modules/History.sys.mjs": typeof import("resource://gre/modules/History.sys.mjs"),
  "resource://gre/modules/IgnoreLists.sys.mjs": typeof import("resource://gre/modules/IgnoreLists.sys.mjs"),
  "resource://gre/modules/IndexedDB.sys.mjs": typeof import("resource://gre/modules/IndexedDB.sys.mjs"),
  "resource://gre/modules/InlineSpellChecker.sys.mjs": typeof import("resource://gre/modules/InlineSpellChecker.sys.mjs"),
  "resource://gre/modules/InlineSpellCheckerContent.sys.mjs": typeof import("resource://gre/modules/InlineSpellCheckerContent.sys.mjs"),
  "resource://gre/modules/InsecurePasswordUtils.sys.mjs": typeof import("resource://gre/modules/InsecurePasswordUtils.sys.mjs"),
  "resource://gre/modules/Integration.sys.mjs": typeof import("resource://gre/modules/Integration.sys.mjs"),
  "resource://gre/modules/JSONFile.sys.mjs": typeof import("resource://gre/modules/JSONFile.sys.mjs"),
  "resource://gre/modules/JsonSchema.sys.mjs": typeof import("resource://gre/modules/JsonSchema.sys.mjs"),
  "resource://gre/modules/KeywordUtils.sys.mjs": typeof import("resource://gre/modules/KeywordUtils.sys.mjs"),
  "resource://gre/modules/LangPackMatcher.sys.mjs": typeof import("resource://gre/modules/LangPackMatcher.sys.mjs"),
  "resource://gre/modules/LayoutUtils.sys.mjs": typeof import("resource://gre/modules/LayoutUtils.sys.mjs"),
  "resource://gre/modules/LightweightThemeConsumer.sys.mjs": typeof import("resource://gre/modules/LightweightThemeConsumer.sys.mjs"),
  "resource://gre/modules/LightweightThemeManager.sys.mjs": typeof import("resource://gre/modules/LightweightThemeManager.sys.mjs"),
  "resource://gre/modules/LoadURIDelegate.sys.mjs": typeof import("resource://gre/modules/LoadURIDelegate.sys.mjs"),
  "resource://gre/modules/LocationHelper.sys.mjs": typeof import("resource://gre/modules/LocationHelper.sys.mjs"),
  "resource://gre/modules/Log.sys.mjs": typeof import("resource://gre/modules/Log.sys.mjs"),
  "resource://gre/modules/LoginAutoComplete.sys.mjs": typeof import("resource://gre/modules/LoginAutoComplete.sys.mjs"),
  "resource://gre/modules/LoginCSVImport.sys.mjs": typeof import("resource://gre/modules/LoginCSVImport.sys.mjs"),
  "resource://gre/modules/LoginExport.sys.mjs": typeof import("resource://gre/modules/LoginExport.sys.mjs"),
  "resource://gre/modules/LoginHelper.sys.mjs": typeof import("resource://gre/modules/LoginHelper.sys.mjs"),
  "resource://gre/modules/LoginManager.shared.sys.mjs": typeof import("resource://gre/modules/LoginManager.shared.sys.mjs"),
  "resource://gre/modules/LoginManagerChild.sys.mjs": typeof import("resource://gre/modules/LoginManagerChild.sys.mjs"),
  "resource://gre/modules/LoginManagerContextMenu.sys.mjs": typeof import("resource://gre/modules/LoginManagerContextMenu.sys.mjs"),
  "resource://gre/modules/LoginManagerParent.sys.mjs": typeof import("resource://gre/modules/LoginManagerParent.sys.mjs"),
  "resource://gre/modules/LoginRecipes.sys.mjs": typeof import("resource://gre/modules/LoginRecipes.sys.mjs"),
  "resource://gre/modules/LoginStore.sys.mjs": typeof import("resource://gre/modules/LoginStore.sys.mjs"),
  "resource://gre/modules/ManifestFinder.sys.mjs": typeof import("resource://gre/modules/ManifestFinder.sys.mjs"),
  "resource://gre/modules/ManifestIcons.sys.mjs": typeof import("resource://gre/modules/ManifestIcons.sys.mjs"),
  "resource://gre/modules/ManifestObtainer.sys.mjs": typeof import("resource://gre/modules/ManifestObtainer.sys.mjs"),
  "resource://gre/modules/MatchURLFilters.sys.mjs": typeof import("resource://gre/modules/MatchURLFilters.sys.mjs"),
  "resource://gre/modules/MediaUtils.sys.mjs": typeof import("resource://gre/modules/MediaUtils.sys.mjs"),
  "resource://gre/modules/MessageManagerProxy.sys.mjs": typeof import("resource://gre/modules/MessageManagerProxy.sys.mjs"),
  "resource://gre/modules/Messaging.sys.mjs": typeof import("resource://gre/modules/Messaging.sys.mjs"),
  "resource://gre/modules/NLP.sys.mjs": typeof import("resource://gre/modules/NLP.sys.mjs"),
  "resource://gre/modules/NativeManifests.sys.mjs": typeof import("resource://gre/modules/NativeManifests.sys.mjs"),
  "resource://gre/modules/NativeMessaging.sys.mjs": typeof import("resource://gre/modules/NativeMessaging.sys.mjs"),
  "resource://gre/modules/NetUtil.sys.mjs": typeof import("resource://gre/modules/NetUtil.sys.mjs"),
  "resource://gre/modules/NewTabUtils.sys.mjs": typeof import("resource://gre/modules/NewTabUtils.sys.mjs"),
  "resource://gre/modules/OSCrypto_win.sys.mjs": typeof import("resource://gre/modules/OSCrypto_win.sys.mjs"),
  "resource://gre/modules/OSKeyStore.sys.mjs": typeof import("resource://gre/modules/OSKeyStore.sys.mjs"),
  "resource://gre/modules/ObjectUtils.sys.mjs": typeof import("resource://gre/modules/ObjectUtils.sys.mjs"),
  "resource://gre/modules/ObliviousHTTP.sys.mjs": typeof import("resource://gre/modules/ObliviousHTTP.sys.mjs"),
  "resource://gre/modules/OsEnvironment.sys.mjs": typeof import("resource://gre/modules/OsEnvironment.sys.mjs"),
  "resource://gre/modules/PageThumbUtils.sys.mjs": typeof import("resource://gre/modules/PageThumbUtils.sys.mjs"),
  "resource://gre/modules/PageThumbs.sys.mjs": typeof import("resource://gre/modules/PageThumbs.sys.mjs"),
  "resource://gre/modules/PermissionsUtils.sys.mjs": typeof import("resource://gre/modules/PermissionsUtils.sys.mjs"),
  "resource://gre/modules/PictureInPicture.sys.mjs": typeof import("resource://gre/modules/PictureInPicture.sys.mjs"),
  "resource://gre/modules/PictureInPictureControls.sys.mjs": typeof import("resource://gre/modules/PictureInPictureControls.sys.mjs"),
  "resource://gre/modules/PlacesBackups.sys.mjs": typeof import("resource://gre/modules/PlacesBackups.sys.mjs"),
  "resource://gre/modules/PlacesDBUtils.sys.mjs": typeof import("resource://gre/modules/PlacesDBUtils.sys.mjs"),
  "resource://gre/modules/PlacesPreviews.sys.mjs": typeof import("resource://gre/modules/PlacesPreviews.sys.mjs"),
  "resource://gre/modules/PlacesQuery.sys.mjs": typeof import("resource://gre/modules/PlacesQuery.sys.mjs"),
  "resource://gre/modules/PlacesSemanticHistoryDatabase.sys.mjs": typeof import("resource://gre/modules/PlacesSemanticHistoryDatabase.sys.mjs"),
  "resource://gre/modules/PlacesSemanticHistoryManager.sys.mjs": typeof import("resource://gre/modules/PlacesSemanticHistoryManager.sys.mjs"),
  "resource://gre/modules/PlacesSyncUtils.sys.mjs": typeof import("resource://gre/modules/PlacesSyncUtils.sys.mjs"),
  "resource://gre/modules/PlacesTransactions.sys.mjs": typeof import("resource://gre/modules/PlacesTransactions.sys.mjs"),
  "resource://gre/modules/PlacesUtils.sys.mjs": typeof import("resource://gre/modules/PlacesUtils.sys.mjs"),
  "resource://gre/modules/Preferences.sys.mjs": typeof import("resource://gre/modules/Preferences.sys.mjs"),
  "resource://gre/modules/PrincipalsCollector.sys.mjs": typeof import("resource://gre/modules/PrincipalsCollector.sys.mjs"),
  "resource://gre/modules/PrivateBrowsingUtils.sys.mjs": typeof import("resource://gre/modules/PrivateBrowsingUtils.sys.mjs"),
  "resource://gre/modules/ProcessType.sys.mjs": typeof import("resource://gre/modules/ProcessType.sys.mjs"),
  "resource://gre/modules/ProfileAge.sys.mjs": typeof import("resource://gre/modules/ProfileAge.sys.mjs"),
  "resource://gre/modules/PromiseWorker.sys.mjs": typeof import("resource://gre/modules/PromiseWorker.sys.mjs"),
  "resource://gre/modules/PromptUtils.sys.mjs": typeof import("resource://gre/modules/PromptUtils.sys.mjs"),
  "resource://gre/modules/PropertyListUtils.sys.mjs": typeof import("resource://gre/modules/PropertyListUtils.sys.mjs"),
  "resource://gre/modules/ProxyChannelFilter.sys.mjs": typeof import("resource://gre/modules/ProxyChannelFilter.sys.mjs"),
  "resource://gre/modules/PushBroadcastService.sys.mjs": typeof import("resource://gre/modules/PushBroadcastService.sys.mjs"),
  "resource://gre/modules/PushCrypto.sys.mjs": typeof import("resource://gre/modules/PushCrypto.sys.mjs"),
  "resource://gre/modules/PushService.sys.mjs": typeof import("resource://gre/modules/PushService.sys.mjs"),
  "resource://gre/modules/PushServiceWebSocket.sys.mjs": typeof import("resource://gre/modules/PushServiceWebSocket.sys.mjs"),
  "resource://gre/modules/Readerable.sys.mjs": typeof import("resource://gre/modules/Readerable.sys.mjs"),
  "resource://gre/modules/Region.sys.mjs": typeof import("resource://gre/modules/Region.sys.mjs"),
  "resource://gre/modules/RemotePageAccessManager.sys.mjs": typeof import("resource://gre/modules/RemotePageAccessManager.sys.mjs"),
  "resource://gre/modules/RemoteSettingsCrashPull.sys.mjs": typeof import("resource://gre/modules/RemoteSettingsCrashPull.sys.mjs"),
  "resource://gre/modules/RemoteWebNavigation.sys.mjs": typeof import("resource://gre/modules/RemoteWebNavigation.sys.mjs"),
  "resource://gre/modules/ResetProfile.sys.mjs": typeof import("resource://gre/modules/ResetProfile.sys.mjs"),
  "resource://gre/modules/RustSharedRemoteSettingsService.sys.mjs": typeof import("resource://gre/modules/RustSharedRemoteSettingsService.sys.mjs"),
  "resource://gre/modules/SafeBrowsing.sys.mjs": typeof import("resource://gre/modules/SafeBrowsing.sys.mjs"),
  "resource://gre/modules/Schemas.sys.mjs": typeof import("resource://gre/modules/Schemas.sys.mjs"),
  "resource://gre/modules/SearchService.sys.mjs": typeof import("resource://gre/modules/SearchService.sys.mjs"),
  "resource://gre/modules/SecurityInfo.sys.mjs": typeof import("resource://gre/modules/SecurityInfo.sys.mjs"),
  "resource://gre/modules/SelectionUtils.sys.mjs": typeof import("resource://gre/modules/SelectionUtils.sys.mjs"),
  "resource://gre/modules/ServiceRequest.sys.mjs": typeof import("resource://gre/modules/ServiceRequest.sys.mjs"),
  "resource://gre/modules/ServiceWorkerCleanUp.sys.mjs": typeof import("resource://gre/modules/ServiceWorkerCleanUp.sys.mjs"),
  "resource://gre/modules/ShortcutUtils.sys.mjs": typeof import("resource://gre/modules/ShortcutUtils.sys.mjs"),
  "resource://gre/modules/Sqlite.sys.mjs": typeof import("resource://gre/modules/Sqlite.sys.mjs"),
  "resource://gre/modules/SubDialog.sys.mjs": typeof import("resource://gre/modules/SubDialog.sys.mjs"),
  "resource://gre/modules/Subprocess.sys.mjs": typeof import("resource://gre/modules/Subprocess.sys.mjs"),
  "resource://gre/modules/SyncedBookmarksMirror.sys.mjs": typeof import("resource://gre/modules/SyncedBookmarksMirror.sys.mjs"),
  "resource://gre/modules/TaskScheduler.sys.mjs": typeof import("resource://gre/modules/TaskScheduler.sys.mjs"),
  "resource://gre/modules/TaskSchedulerMacOSImpl.sys.mjs": typeof import("resource://gre/modules/TaskSchedulerMacOSImpl.sys.mjs"),
  "resource://gre/modules/TaskSchedulerWinImpl.sys.mjs": typeof import("resource://gre/modules/TaskSchedulerWinImpl.sys.mjs"),
  "resource://gre/modules/TelemetryArchive.sys.mjs": typeof import("resource://gre/modules/TelemetryArchive.sys.mjs"),
  "resource://gre/modules/TelemetryController.sys.mjs": typeof import("resource://gre/modules/TelemetryController.sys.mjs"),
  "resource://gre/modules/TelemetryEnvironment.sys.mjs": typeof import("resource://gre/modules/TelemetryEnvironment.sys.mjs"),
  "resource://gre/modules/TelemetryReportingPolicy.sys.mjs": typeof import("resource://gre/modules/TelemetryReportingPolicy.sys.mjs"),
  "resource://gre/modules/TelemetryScheduler.sys.mjs": typeof import("resource://gre/modules/TelemetryScheduler.sys.mjs"),
  "resource://gre/modules/TelemetrySend.sys.mjs": typeof import("resource://gre/modules/TelemetrySend.sys.mjs"),
  "resource://gre/modules/TelemetrySession.sys.mjs": typeof import("resource://gre/modules/TelemetrySession.sys.mjs"),
  "resource://gre/modules/TelemetryStorage.sys.mjs": typeof import("resource://gre/modules/TelemetryStorage.sys.mjs"),
  "resource://gre/modules/TelemetryTimestamps.sys.mjs": typeof import("resource://gre/modules/TelemetryTimestamps.sys.mjs"),
  "resource://gre/modules/TelemetryUtils.sys.mjs": typeof import("resource://gre/modules/TelemetryUtils.sys.mjs"),
  "resource://gre/modules/Timer.sys.mjs": typeof import("resource://gre/modules/Timer.sys.mjs"),
  "resource://gre/modules/UninstallPing.sys.mjs": typeof import("resource://gre/modules/UninstallPing.sys.mjs"),
  "resource://gre/modules/UntrustedModulesPing.sys.mjs": typeof import("resource://gre/modules/UntrustedModulesPing.sys.mjs"),
  "resource://gre/modules/UpdateListener.sys.mjs": typeof import("resource://gre/modules/UpdateListener.sys.mjs"),
  "resource://gre/modules/UpdateLog.sys.mjs": typeof import("resource://gre/modules/UpdateLog.sys.mjs"),
  "resource://gre/modules/UpdatePing.sys.mjs": typeof import("resource://gre/modules/UpdatePing.sys.mjs"),
  "resource://gre/modules/UpdateService.sys.mjs": typeof import("resource://gre/modules/UpdateService.sys.mjs"),
  "resource://gre/modules/UpdateUtils.sys.mjs": typeof import("resource://gre/modules/UpdateUtils.sys.mjs"),
  "resource://gre/modules/UsageReporting.sys.mjs": typeof import("resource://gre/modules/UsageReporting.sys.mjs"),
  "resource://gre/modules/WebAuthnFeature.sys.mjs": typeof import("resource://gre/modules/WebAuthnFeature.sys.mjs"),
  "resource://gre/modules/WebChannel.sys.mjs": typeof import("resource://gre/modules/WebChannel.sys.mjs"),
  "resource://gre/modules/WebNavigation.sys.mjs": typeof import("resource://gre/modules/WebNavigation.sys.mjs"),
  "resource://gre/modules/WebNavigationFrames.sys.mjs": typeof import("resource://gre/modules/WebNavigationFrames.sys.mjs"),
  "resource://gre/modules/WebRequest.sys.mjs": typeof import("resource://gre/modules/WebRequest.sys.mjs"),
  "resource://gre/modules/WebRequestUpload.sys.mjs": typeof import("resource://gre/modules/WebRequestUpload.sys.mjs"),
  "resource://gre/modules/WindowsLaunchOnLogin.sys.mjs": typeof import("resource://gre/modules/WindowsLaunchOnLogin.sys.mjs"),
  "resource://gre/modules/WindowsRegistry.sys.mjs": typeof import("resource://gre/modules/WindowsRegistry.sys.mjs"),
  "resource://gre/modules/addons/AddonRepository.sys.mjs": typeof import("resource://gre/modules/addons/AddonRepository.sys.mjs"),
  "resource://gre/modules/addons/AddonSettings.sys.mjs": typeof import("resource://gre/modules/addons/AddonSettings.sys.mjs"),
  "resource://gre/modules/addons/ProductAddonChecker.sys.mjs": typeof import("resource://gre/modules/addons/ProductAddonChecker.sys.mjs"),
  "resource://gre/modules/addons/XPIDatabase.sys.mjs": typeof import("resource://gre/modules/addons/XPIDatabase.sys.mjs"),
  "resource://gre/modules/addons/XPIInstall.sys.mjs": typeof import("resource://gre/modules/addons/XPIInstall.sys.mjs"),
  "resource://gre/modules/addons/XPIProvider.sys.mjs": typeof import("resource://gre/modules/addons/XPIProvider.sys.mjs"),
  "resource://gre/modules/addons/crypto-utils.sys.mjs": typeof import("resource://gre/modules/addons/crypto-utils.sys.mjs"),
  "resource://gre/modules/addons/siteperms-addon-utils.sys.mjs": typeof import("resource://gre/modules/addons/siteperms-addon-utils.sys.mjs"),
  "resource://gre/modules/components-utils/ClientEnvironment.sys.mjs": typeof import("resource://gre/modules/components-utils/ClientEnvironment.sys.mjs"),
  "resource://gre/modules/components-utils/FilterExpressions.sys.mjs": typeof import("resource://gre/modules/components-utils/FilterExpressions.sys.mjs"),
  "resource://gre/modules/components-utils/JsonSchemaValidator.sys.mjs": typeof import("resource://gre/modules/components-utils/JsonSchemaValidator.sys.mjs"),
  "resource://gre/modules/components-utils/Sampling.sys.mjs": typeof import("resource://gre/modules/components-utils/Sampling.sys.mjs"),
  "resource://gre/modules/components-utils/WindowsInstallsInfo.sys.mjs": typeof import("resource://gre/modules/components-utils/WindowsInstallsInfo.sys.mjs"),
  "resource://gre/modules/components-utils/WindowsVersionInfo.sys.mjs": typeof import("resource://gre/modules/components-utils/WindowsVersionInfo.sys.mjs"),
  "resource://gre/modules/components-utils/mozjexl.sys.mjs": typeof import("resource://gre/modules/components-utils/mozjexl.sys.mjs"),
  "resource://gre/modules/contentrelevancy/private/InputUtils.sys.mjs": typeof import("resource://gre/modules/contentrelevancy/private/InputUtils.sys.mjs"),
  "resource://gre/modules/ctypes.sys.mjs": typeof import("resource://gre/modules/ctypes.sys.mjs"),
  "resource://gre/modules/handlers/HandlerList.sys.mjs": typeof import("resource://gre/modules/handlers/HandlerList.sys.mjs"),
  "resource://gre/modules/jsdebugger.sys.mjs": typeof import("resource://gre/modules/jsdebugger.sys.mjs"),
  "resource://gre/modules/kvstore.sys.mjs": typeof import("resource://gre/modules/kvstore.sys.mjs"),
  "resource://gre/modules/media/IdpSandbox.sys.mjs": typeof import("resource://gre/modules/media/IdpSandbox.sys.mjs"),
  "resource://gre/modules/media/PeerConnectionIdp.sys.mjs": typeof import("resource://gre/modules/media/PeerConnectionIdp.sys.mjs"),
  "resource://gre/modules/narrate/NarrateControls.sys.mjs": typeof import("resource://gre/modules/narrate/NarrateControls.sys.mjs"),
  "resource://gre/modules/policies/WindowsGPOParser.sys.mjs": typeof import("resource://gre/modules/policies/WindowsGPOParser.sys.mjs"),
  "resource://gre/modules/policies/macOSPoliciesParser.sys.mjs": typeof import("resource://gre/modules/policies/macOSPoliciesParser.sys.mjs"),
  "resource://gre/modules/psm/RemoteSecuritySettings.sys.mjs": typeof import("resource://gre/modules/psm/RemoteSecuritySettings.sys.mjs"),
  "resource://gre/modules/reflect.sys.mjs": typeof import("resource://gre/modules/reflect.sys.mjs"),
  "resource://gre/modules/sessionstore/PrivacyFilter.sys.mjs": typeof import("resource://gre/modules/sessionstore/PrivacyFilter.sys.mjs"),
  "resource://gre/modules/sessionstore/PrivacyLevel.sys.mjs": typeof import("resource://gre/modules/sessionstore/PrivacyLevel.sys.mjs"),
  "resource://gre/modules/sessionstore/SessionHistory.sys.mjs": typeof import("resource://gre/modules/sessionstore/SessionHistory.sys.mjs"),
  "resource://gre/modules/sessionstore/SessionStoreHelper.sys.mjs": typeof import("resource://gre/modules/sessionstore/SessionStoreHelper.sys.mjs"),
  "resource://gre/modules/shared/AddressComponent.sys.mjs": typeof import("resource://gre/modules/shared/AddressComponent.sys.mjs"),
  "resource://gre/modules/shared/AddressMetaData.sys.mjs": typeof import("resource://gre/modules/shared/AddressMetaData.sys.mjs"),
  "resource://gre/modules/shared/AddressMetaDataExtension.sys.mjs": typeof import("resource://gre/modules/shared/AddressMetaDataExtension.sys.mjs"),
  "resource://gre/modules/shared/AddressMetaDataLoader.sys.mjs": typeof import("resource://gre/modules/shared/AddressMetaDataLoader.sys.mjs"),
  "resource://gre/modules/shared/AddressParser.sys.mjs": typeof import("resource://gre/modules/shared/AddressParser.sys.mjs"),
  "resource://gre/modules/shared/AutofillFormFactory.sys.mjs": typeof import("resource://gre/modules/shared/AutofillFormFactory.sys.mjs"),
  "resource://gre/modules/shared/AutofillTelemetry.sys.mjs": typeof import("resource://gre/modules/shared/AutofillTelemetry.sys.mjs"),
  "resource://gre/modules/shared/CreditCardRecord.sys.mjs": typeof import("resource://gre/modules/shared/CreditCardRecord.sys.mjs"),
  "resource://gre/modules/shared/CreditCardRuleset.sys.mjs": typeof import("resource://gre/modules/shared/CreditCardRuleset.sys.mjs"),
  "resource://gre/modules/shared/FieldScanner.sys.mjs": typeof import("resource://gre/modules/shared/FieldScanner.sys.mjs"),
  "resource://gre/modules/shared/FormAutofillHandler.sys.mjs": typeof import("resource://gre/modules/shared/FormAutofillHandler.sys.mjs"),
  "resource://gre/modules/shared/FormAutofillHeuristics.sys.mjs": typeof import("resource://gre/modules/shared/FormAutofillHeuristics.sys.mjs"),
  "resource://gre/modules/shared/FormAutofillNameUtils.sys.mjs": typeof import("resource://gre/modules/shared/FormAutofillNameUtils.sys.mjs"),
  "resource://gre/modules/shared/FormAutofillSection.sys.mjs": typeof import("resource://gre/modules/shared/FormAutofillSection.sys.mjs"),
  "resource://gre/modules/shared/FormAutofillUtils.sys.mjs": typeof import("resource://gre/modules/shared/FormAutofillUtils.sys.mjs"),
  "resource://gre/modules/shared/FormStateManager.sys.mjs": typeof import("resource://gre/modules/shared/FormStateManager.sys.mjs"),
  "resource://gre/modules/shared/LabelUtils.sys.mjs": typeof import("resource://gre/modules/shared/LabelUtils.sys.mjs"),
  "resource://gre/modules/shared/LoginFormFactory.sys.mjs": typeof import("resource://gre/modules/shared/LoginFormFactory.sys.mjs"),
  "resource://gre/modules/shared/PasswordGenerator.sys.mjs": typeof import("resource://gre/modules/shared/PasswordGenerator.sys.mjs"),
  "resource://gre/modules/shared/PasswordRulesParser.sys.mjs": typeof import("resource://gre/modules/shared/PasswordRulesParser.sys.mjs"),
  "resource://gre/modules/shared/PhoneNumber.sys.mjs": typeof import("resource://gre/modules/shared/PhoneNumber.sys.mjs"),
  "resource://gre/modules/shared/PhoneNumberNormalizer.sys.mjs": typeof import("resource://gre/modules/shared/PhoneNumberNormalizer.sys.mjs"),
  "resource://gre/modules/subprocess/subprocess_unix.sys.mjs": typeof import("resource://gre/modules/subprocess/subprocess_unix.sys.mjs"),
  "resource://gre/modules/subprocess/subprocess_win.sys.mjs": typeof import("resource://gre/modules/subprocess/subprocess_win.sys.mjs"),
  "resource://gre/modules/translations/LanguageDetector.sys.mjs": typeof import("resource://gre/modules/translations/LanguageDetector.sys.mjs"),
  "resource://gre/modules/workers/PromiseWorker.mjs": typeof import("resource://gre/modules/workers/PromiseWorker.mjs"),
  "resource://messaging-system/lib/SpecialMessageActions.sys.mjs": typeof import("resource://messaging-system/lib/SpecialMessageActions.sys.mjs"),
  "resource://messaging-system/targeting/Targeting.sys.mjs": typeof import("resource://messaging-system/targeting/Targeting.sys.mjs"),
  "resource://mozscreenshots/Screenshot.sys.mjs": typeof import("resource://mozscreenshots/Screenshot.sys.mjs"),
  "resource://newtab/common/Actions.mjs": typeof import("resource://newtab/common/Actions.mjs"),
  "resource://newtab/lib/AboutPreferences.sys.mjs": typeof import("resource://newtab/lib/AboutPreferences.sys.mjs"),
  "resource://newtab/lib/ActivityStream.sys.mjs": typeof import("resource://newtab/lib/ActivityStream.sys.mjs"),
  "resource://newtab/lib/ActivityStreamMessageChannel.sys.mjs": typeof import("resource://newtab/lib/ActivityStreamMessageChannel.sys.mjs"),
  "resource://newtab/lib/ActivityStreamPrefs.sys.mjs": typeof import("resource://newtab/lib/ActivityStreamPrefs.sys.mjs"),
  "resource://newtab/lib/AdsFeed.sys.mjs": typeof import("resource://newtab/lib/AdsFeed.sys.mjs"),
  "resource://newtab/lib/DefaultSites.sys.mjs": typeof import("resource://newtab/lib/DefaultSites.sys.mjs"),
  "resource://newtab/lib/DiscoveryStreamFeed.sys.mjs": typeof import("resource://newtab/lib/DiscoveryStreamFeed.sys.mjs"),
  "resource://newtab/lib/DownloadsManager.sys.mjs": typeof import("resource://newtab/lib/DownloadsManager.sys.mjs"),
  "resource://newtab/lib/FaviconFeed.sys.mjs": typeof import("resource://newtab/lib/FaviconFeed.sys.mjs"),
  "resource://newtab/lib/HighlightsFeed.sys.mjs": typeof import("resource://newtab/lib/HighlightsFeed.sys.mjs"),
  "resource://newtab/lib/InferredModel/FeatureModel.sys.mjs": typeof import("resource://newtab/lib/InferredModel/FeatureModel.sys.mjs"),
  "resource://newtab/lib/InferredPersonalizationFeed.sys.mjs": typeof import("resource://newtab/lib/InferredPersonalizationFeed.sys.mjs"),
  "resource://newtab/lib/NewTabContentPing.sys.mjs": typeof import("resource://newtab/lib/NewTabContentPing.sys.mjs"),
  "resource://newtab/lib/NewTabGleanUtils.sys.mjs": typeof import("resource://newtab/lib/NewTabGleanUtils.sys.mjs"),
  "resource://newtab/lib/NewTabInit.sys.mjs": typeof import("resource://newtab/lib/NewTabInit.sys.mjs"),
  "resource://newtab/lib/NewTabMessaging.sys.mjs": typeof import("resource://newtab/lib/NewTabMessaging.sys.mjs"),
  "resource://newtab/lib/PersistentCache.sys.mjs": typeof import("resource://newtab/lib/PersistentCache.sys.mjs"),
  "resource://newtab/lib/PersonalityProvider/PersonalityProvider.sys.mjs": typeof import("resource://newtab/lib/PersonalityProvider/PersonalityProvider.sys.mjs"),
  "resource://newtab/lib/PlacesFeed.sys.mjs": typeof import("resource://newtab/lib/PlacesFeed.sys.mjs"),
  "resource://newtab/lib/PrefsFeed.sys.mjs": typeof import("resource://newtab/lib/PrefsFeed.sys.mjs"),
  "resource://newtab/lib/RecommendationProvider.sys.mjs": typeof import("resource://newtab/lib/RecommendationProvider.sys.mjs"),
  "resource://newtab/lib/Screenshots.sys.mjs": typeof import("resource://newtab/lib/Screenshots.sys.mjs"),
  "resource://newtab/lib/SectionsManager.sys.mjs": typeof import("resource://newtab/lib/SectionsManager.sys.mjs"),
  "resource://newtab/lib/StartupCacheInit.sys.mjs": typeof import("resource://newtab/lib/StartupCacheInit.sys.mjs"),
  "resource://newtab/lib/Store.sys.mjs": typeof import("resource://newtab/lib/Store.sys.mjs"),
  "resource://newtab/lib/SystemTickFeed.sys.mjs": typeof import("resource://newtab/lib/SystemTickFeed.sys.mjs"),
  "resource://newtab/lib/TelemetryFeed.sys.mjs": typeof import("resource://newtab/lib/TelemetryFeed.sys.mjs"),
  "resource://newtab/lib/TopSitesFeed.sys.mjs": typeof import("resource://newtab/lib/TopSitesFeed.sys.mjs"),
  "resource://newtab/lib/TopStoriesFeed.sys.mjs": typeof import("resource://newtab/lib/TopStoriesFeed.sys.mjs"),
  "resource://newtab/lib/TrendingSearchFeed.sys.mjs": typeof import("resource://newtab/lib/TrendingSearchFeed.sys.mjs"),
  "resource://newtab/lib/UTEventReporting.sys.mjs": typeof import("resource://newtab/lib/UTEventReporting.sys.mjs"),
  "resource://newtab/lib/WallpaperFeed.sys.mjs": typeof import("resource://newtab/lib/WallpaperFeed.sys.mjs"),
  "resource://newtab/lib/WeatherFeed.sys.mjs": typeof import("resource://newtab/lib/WeatherFeed.sys.mjs"),
  "resource://nimbus/ExperimentAPI.sys.mjs": typeof import("resource://nimbus/ExperimentAPI.sys.mjs"),
  "resource://nimbus/FeatureManifest.sys.mjs": typeof import("resource://nimbus/FeatureManifest.sys.mjs"),
  "resource://nimbus/FirefoxLabs.sys.mjs": typeof import("resource://nimbus/FirefoxLabs.sys.mjs"),
  "resource://nimbus/lib/ExperimentManager.sys.mjs": typeof import("resource://nimbus/lib/ExperimentManager.sys.mjs"),
  "resource://nimbus/lib/ExperimentStore.sys.mjs": typeof import("resource://nimbus/lib/ExperimentStore.sys.mjs"),
  "resource://nimbus/lib/Migrations.sys.mjs": typeof import("resource://nimbus/lib/Migrations.sys.mjs"),
  "resource://nimbus/lib/RemoteSettingsExperimentLoader.sys.mjs": typeof import("resource://nimbus/lib/RemoteSettingsExperimentLoader.sys.mjs"),
  "resource://nimbus/lib/TargetingContextRecorder.sys.mjs": typeof import("resource://nimbus/lib/TargetingContextRecorder.sys.mjs"),
  "resource://nimbus/lib/Telemetry.sys.mjs": typeof import("resource://nimbus/lib/Telemetry.sys.mjs"),
  "resource://normandy-content/AboutPages.sys.mjs": typeof import("resource://normandy-content/AboutPages.sys.mjs"),
  "resource://normandy/Normandy.sys.mjs": typeof import("resource://normandy/Normandy.sys.mjs"),
  "resource://normandy/NormandyMigrations.sys.mjs": typeof import("resource://normandy/NormandyMigrations.sys.mjs"),
  "resource://normandy/actions/AddonRollbackAction.sys.mjs": typeof import("resource://normandy/actions/AddonRollbackAction.sys.mjs"),
  "resource://normandy/actions/AddonRolloutAction.sys.mjs": typeof import("resource://normandy/actions/AddonRolloutAction.sys.mjs"),
  "resource://normandy/actions/BaseAction.sys.mjs": typeof import("resource://normandy/actions/BaseAction.sys.mjs"),
  "resource://normandy/actions/BranchedAddonStudyAction.sys.mjs": typeof import("resource://normandy/actions/BranchedAddonStudyAction.sys.mjs"),
  "resource://normandy/actions/ConsoleLogAction.sys.mjs": typeof import("resource://normandy/actions/ConsoleLogAction.sys.mjs"),
  "resource://normandy/actions/PreferenceExperimentAction.sys.mjs": typeof import("resource://normandy/actions/PreferenceExperimentAction.sys.mjs"),
  "resource://normandy/actions/PreferenceRollbackAction.sys.mjs": typeof import("resource://normandy/actions/PreferenceRollbackAction.sys.mjs"),
  "resource://normandy/actions/PreferenceRolloutAction.sys.mjs": typeof import("resource://normandy/actions/PreferenceRolloutAction.sys.mjs"),
  "resource://normandy/actions/ShowHeartbeatAction.sys.mjs": typeof import("resource://normandy/actions/ShowHeartbeatAction.sys.mjs"),
  "resource://normandy/actions/schemas/index.sys.mjs": typeof import("resource://normandy/actions/schemas/index.sys.mjs"),
  "resource://normandy/lib/ActionsManager.sys.mjs": typeof import("resource://normandy/lib/ActionsManager.sys.mjs"),
  "resource://normandy/lib/AddonRollouts.sys.mjs": typeof import("resource://normandy/lib/AddonRollouts.sys.mjs"),
  "resource://normandy/lib/AddonStudies.sys.mjs": typeof import("resource://normandy/lib/AddonStudies.sys.mjs"),
  "resource://normandy/lib/CleanupManager.sys.mjs": typeof import("resource://normandy/lib/CleanupManager.sys.mjs"),
  "resource://normandy/lib/ClientEnvironment.sys.mjs": typeof import("resource://normandy/lib/ClientEnvironment.sys.mjs"),
  "resource://normandy/lib/Heartbeat.sys.mjs": typeof import("resource://normandy/lib/Heartbeat.sys.mjs"),
  "resource://normandy/lib/LegacyHeartbeat.sys.mjs": typeof import("resource://normandy/lib/LegacyHeartbeat.sys.mjs"),
  "resource://normandy/lib/LogManager.sys.mjs": typeof import("resource://normandy/lib/LogManager.sys.mjs"),
  "resource://normandy/lib/NormandyAddonManager.sys.mjs": typeof import("resource://normandy/lib/NormandyAddonManager.sys.mjs"),
  "resource://normandy/lib/NormandyApi.sys.mjs": typeof import("resource://normandy/lib/NormandyApi.sys.mjs"),
  "resource://normandy/lib/NormandyUtils.sys.mjs": typeof import("resource://normandy/lib/NormandyUtils.sys.mjs"),
  "resource://normandy/lib/PrefUtils.sys.mjs": typeof import("resource://normandy/lib/PrefUtils.sys.mjs"),
  "resource://normandy/lib/PreferenceExperiments.sys.mjs": typeof import("resource://normandy/lib/PreferenceExperiments.sys.mjs"),
  "resource://normandy/lib/PreferenceRollouts.sys.mjs": typeof import("resource://normandy/lib/PreferenceRollouts.sys.mjs"),
  "resource://normandy/lib/RecipeRunner.sys.mjs": typeof import("resource://normandy/lib/RecipeRunner.sys.mjs"),
  "resource://normandy/lib/ShieldPreferences.sys.mjs": typeof import("resource://normandy/lib/ShieldPreferences.sys.mjs"),
  "resource://normandy/lib/Storage.sys.mjs": typeof import("resource://normandy/lib/Storage.sys.mjs"),
  "resource://normandy/lib/TelemetryEvents.sys.mjs": typeof import("resource://normandy/lib/TelemetryEvents.sys.mjs"),
  "resource://normandy/lib/Uptake.sys.mjs": typeof import("resource://normandy/lib/Uptake.sys.mjs"),
  "resource://pdf.js/PdfJs.sys.mjs": typeof import("resource://pdf.js/PdfJs.sys.mjs"),
  "resource://pdf.js/PdfJsNetwork.sys.mjs": typeof import("resource://pdf.js/PdfJsNetwork.sys.mjs"),
  "resource://pdf.js/PdfJsTelemetry.sys.mjs": typeof import("resource://pdf.js/PdfJsTelemetry.sys.mjs"),
  "resource://pdf.js/PdfSandbox.sys.mjs": typeof import("resource://pdf.js/PdfSandbox.sys.mjs"),
  "resource://pdf.js/PdfStreamConverter.sys.mjs": typeof import("resource://pdf.js/PdfStreamConverter.sys.mjs"),
  "resource://services-common/async.sys.mjs": typeof import("resource://services-common/async.sys.mjs"),
  "resource://services-common/kinto-http-client.sys.mjs": typeof import("resource://services-common/kinto-http-client.sys.mjs"),
  "resource://services-common/kinto-offline-client.sys.mjs": typeof import("resource://services-common/kinto-offline-client.sys.mjs"),
  "resource://services-common/kinto-storage-adapter.sys.mjs": typeof import("resource://services-common/kinto-storage-adapter.sys.mjs"),
  "resource://services-common/observers.sys.mjs": typeof import("resource://services-common/observers.sys.mjs"),
  "resource://services-common/uptake-telemetry.sys.mjs": typeof import("resource://services-common/uptake-telemetry.sys.mjs"),
  "resource://services-common/utils.sys.mjs": typeof import("resource://services-common/utils.sys.mjs"),
  "resource://services-crypto/jwcrypto.sys.mjs": typeof import("resource://services-crypto/jwcrypto.sys.mjs"),
  "resource://services-crypto/utils.sys.mjs": typeof import("resource://services-crypto/utils.sys.mjs"),
  "resource://services-settings/Attachments.sys.mjs": typeof import("resource://services-settings/Attachments.sys.mjs"),
  "resource://services-settings/Database.sys.mjs": typeof import("resource://services-settings/Database.sys.mjs"),
  "resource://services-settings/IDBHelpers.sys.mjs": typeof import("resource://services-settings/IDBHelpers.sys.mjs"),
  "resource://services-settings/RemoteSettingsClient.sys.mjs": typeof import("resource://services-settings/RemoteSettingsClient.sys.mjs"),
  "resource://services-settings/RemoteSettingsWorker.sys.mjs": typeof import("resource://services-settings/RemoteSettingsWorker.sys.mjs"),
  "resource://services-settings/SharedUtils.sys.mjs": typeof import("resource://services-settings/SharedUtils.sys.mjs"),
  "resource://services-settings/SyncHistory.sys.mjs": typeof import("resource://services-settings/SyncHistory.sys.mjs"),
  "resource://services-settings/Utils.sys.mjs": typeof import("resource://services-settings/Utils.sys.mjs"),
  "resource://services-settings/remote-settings.sys.mjs": typeof import("resource://services-settings/remote-settings.sys.mjs"),
  "resource://services-sync/SyncedTabs.sys.mjs": typeof import("resource://services-sync/SyncedTabs.sys.mjs"),
  "resource://services-sync/TabsStore.sys.mjs": typeof import("resource://services-sync/TabsStore.sys.mjs"),
  "resource://services-sync/UIState.sys.mjs": typeof import("resource://services-sync/UIState.sys.mjs"),
  "resource://services-sync/constants.sys.mjs": typeof import("resource://services-sync/constants.sys.mjs"),
  "resource://services-sync/doctor.sys.mjs": typeof import("resource://services-sync/doctor.sys.mjs"),
  "resource://services-sync/engines/addons.sys.mjs": typeof import("resource://services-sync/engines/addons.sys.mjs"),
  "resource://services-sync/engines/forms.sys.mjs": typeof import("resource://services-sync/engines/forms.sys.mjs"),
  "resource://services-sync/engines/passwords.sys.mjs": typeof import("resource://services-sync/engines/passwords.sys.mjs"),
  "resource://services-sync/keys.sys.mjs": typeof import("resource://services-sync/keys.sys.mjs"),
  "resource://services-sync/main.sys.mjs": typeof import("resource://services-sync/main.sys.mjs"),
  "resource://services-sync/record.sys.mjs": typeof import("resource://services-sync/record.sys.mjs"),
  "resource://services-sync/resource.sys.mjs": typeof import("resource://services-sync/resource.sys.mjs"),
  "resource://services-sync/service.sys.mjs": typeof import("resource://services-sync/service.sys.mjs"),
  "resource://services-sync/status.sys.mjs": typeof import("resource://services-sync/status.sys.mjs"),
  "resource://services-sync/sync_auth.sys.mjs": typeof import("resource://services-sync/sync_auth.sys.mjs"),
  "resource://services-sync/telemetry.sys.mjs": typeof import("resource://services-sync/telemetry.sys.mjs"),
  "resource://services-sync/util.sys.mjs": typeof import("resource://services-sync/util.sys.mjs"),
  "resource://talos-powers/TalosParentProfiler.sys.mjs": typeof import("resource://talos-powers/TalosParentProfiler.sys.mjs"),
  "resource://test/es6module_devtoolsLoader.js": typeof import("resource://test/es6module_devtoolsLoader.js"),
  "resource://test/esm_lazy-1.sys.mjs": typeof import("resource://test/esm_lazy-1.sys.mjs"),
  "resource://test/esm_lazy-2.sys.mjs": typeof import("resource://test/esm_lazy-2.sys.mjs"),
  "resource://test/esmified-1.sys.mjs": typeof import("resource://test/esmified-1.sys.mjs"),
  "resource://testing-common/AddonTestUtils.sys.mjs": typeof import("resource://testing-common/AddonTestUtils.sys.mjs"),
  "resource://testing-common/AppInfo.sys.mjs": typeof import("resource://testing-common/AppInfo.sys.mjs"),
  "resource://testing-common/AppUiTestDelegate.sys.mjs": typeof import("resource://testing-common/AppUiTestDelegate.sys.mjs"),
  "resource://testing-common/Assert.sys.mjs": typeof import("resource://testing-common/Assert.sys.mjs"),
  "resource://testing-common/BrowserTestUtils.sys.mjs": typeof import("resource://testing-common/BrowserTestUtils.sys.mjs"),
  "resource://testing-common/ContentTask.sys.mjs": typeof import("resource://testing-common/ContentTask.sys.mjs"),
  "resource://testing-common/ContentTaskUtils.sys.mjs": typeof import("resource://testing-common/ContentTaskUtils.sys.mjs"),
  "resource://testing-common/CustomizableUITestUtils.sys.mjs": typeof import("resource://testing-common/CustomizableUITestUtils.sys.mjs"),
  "resource://testing-common/DoHTestUtils.sys.mjs": typeof import("resource://testing-common/DoHTestUtils.sys.mjs"),
  "resource://testing-common/EnterprisePolicyTesting.sys.mjs": typeof import("resource://testing-common/EnterprisePolicyTesting.sys.mjs"),
  "resource://testing-common/ExtensionTestCommon.sys.mjs": typeof import("resource://testing-common/ExtensionTestCommon.sys.mjs"),
  "resource://testing-common/ExtensionXPCShellUtils.sys.mjs": typeof import("resource://testing-common/ExtensionXPCShellUtils.sys.mjs"),
  "resource://testing-common/FileTestUtils.sys.mjs": typeof import("resource://testing-common/FileTestUtils.sys.mjs"),
  "resource://testing-common/FormHistoryTestUtils.sys.mjs": typeof import("resource://testing-common/FormHistoryTestUtils.sys.mjs"),
  "resource://testing-common/MerinoTestUtils.sys.mjs": typeof import("resource://testing-common/MerinoTestUtils.sys.mjs"),
  "resource://testing-common/MessageChannel.sys.mjs": typeof import("resource://testing-common/MessageChannel.sys.mjs"),
  "resource://testing-common/MockColorPicker.sys.mjs": typeof import("resource://testing-common/MockColorPicker.sys.mjs"),
  "resource://testing-common/MockFilePicker.sys.mjs": typeof import("resource://testing-common/MockFilePicker.sys.mjs"),
  "resource://testing-common/MockPermissionPrompt.sys.mjs": typeof import("resource://testing-common/MockPermissionPrompt.sys.mjs"),
  "resource://testing-common/MockPromptCollection.sys.mjs": typeof import("resource://testing-common/MockPromptCollection.sys.mjs"),
  "resource://testing-common/MockRegistrar.sys.mjs": typeof import("resource://testing-common/MockRegistrar.sys.mjs"),
  "resource://testing-common/MockRegistry.sys.mjs": typeof import("resource://testing-common/MockRegistry.sys.mjs"),
  "resource://testing-common/MockSound.sys.mjs": typeof import("resource://testing-common/MockSound.sys.mjs"),
  "resource://testing-common/NimbusTestUtils.sys.mjs": typeof import("resource://testing-common/NimbusTestUtils.sys.mjs"),
  "resource://testing-common/NormandyTestUtils.sys.mjs": typeof import("resource://testing-common/NormandyTestUtils.sys.mjs"),
  "resource://testing-common/PerTestCoverageUtils.sys.mjs": typeof import("resource://testing-common/PerTestCoverageUtils.sys.mjs"),
  "resource://testing-common/PerfTestHelpers.sys.mjs": typeof import("resource://testing-common/PerfTestHelpers.sys.mjs"),
  "resource://testing-common/PermissionTestUtils.sys.mjs": typeof import("resource://testing-common/PermissionTestUtils.sys.mjs"),
  "resource://testing-common/PlacesTestUtils.sys.mjs": typeof import("resource://testing-common/PlacesTestUtils.sys.mjs"),
  "resource://testing-common/PromiseTestUtils.sys.mjs": typeof import("resource://testing-common/PromiseTestUtils.sys.mjs"),
  "resource://testing-common/PromptTestUtils.sys.mjs": typeof import("resource://testing-common/PromptTestUtils.sys.mjs"),
  "resource://testing-common/QuickSuggestTestUtils.sys.mjs": typeof import("resource://testing-common/QuickSuggestTestUtils.sys.mjs"),
  "resource://testing-common/RegionTestUtils.sys.mjs": typeof import("resource://testing-common/RegionTestUtils.sys.mjs"),
  "resource://testing-common/RemoteSettingsServer.sys.mjs": typeof import("resource://testing-common/RemoteSettingsServer.sys.mjs"),
  "resource://testing-common/SearchTestUtils.sys.mjs": typeof import("resource://testing-common/SearchTestUtils.sys.mjs"),
  "resource://testing-common/SearchUITestUtils.sys.mjs": typeof import("resource://testing-common/SearchUITestUtils.sys.mjs"),
  "resource://testing-common/Sinon.sys.mjs": typeof import("resource://testing-common/Sinon.sys.mjs"),
  "resource://testing-common/SiteDataTestUtils.sys.mjs": typeof import("resource://testing-common/SiteDataTestUtils.sys.mjs"),
  "resource://testing-common/SpecialPowersParent.sys.mjs": typeof import("resource://testing-common/SpecialPowersParent.sys.mjs"),
  "resource://testing-common/SpecialPowersProcessActor.sys.mjs": typeof import("resource://testing-common/SpecialPowersProcessActor.sys.mjs"),
  "resource://testing-common/SpecialPowersSandbox.sys.mjs": typeof import("resource://testing-common/SpecialPowersSandbox.sys.mjs"),
  "resource://testing-common/TabGroupTestUtils.sys.mjs": typeof import("resource://testing-common/TabGroupTestUtils.sys.mjs"),
  "resource://testing-common/TelemetryArchiveTesting.sys.mjs": typeof import("resource://testing-common/TelemetryArchiveTesting.sys.mjs"),
  "resource://testing-common/TelemetryTestUtils.sys.mjs": typeof import("resource://testing-common/TelemetryTestUtils.sys.mjs"),
  "resource://testing-common/TestIntegration.sys.mjs": typeof import("resource://testing-common/TestIntegration.sys.mjs"),
  "resource://testing-common/TestUtils.sys.mjs": typeof import("resource://testing-common/TestUtils.sys.mjs"),
  "resource://testing-common/UrlbarTestUtils.sys.mjs": typeof import("resource://testing-common/UrlbarTestUtils.sys.mjs"),
  "resource://testing-common/WrapPrivileged.sys.mjs": typeof import("resource://testing-common/WrapPrivileged.sys.mjs"),
  "resource://testing-common/XPCShellContentUtils.sys.mjs": typeof import("resource://testing-common/XPCShellContentUtils.sys.mjs"),
  "resource://testing-common/httpd.sys.mjs": typeof import("resource://testing-common/httpd.sys.mjs"),
  "resource://tps/auth/fxaccounts.sys.mjs": typeof import("resource://tps/auth/fxaccounts.sys.mjs"),
  "resource://tps/logger.sys.mjs": typeof import("resource://tps/logger.sys.mjs"),
  "resource://tps/modules/addons.sys.mjs": typeof import("resource://tps/modules/addons.sys.mjs"),
  "resource://tps/modules/bookmarkValidator.sys.mjs": typeof import("resource://tps/modules/bookmarkValidator.sys.mjs"),
  "resource://tps/modules/bookmarks.sys.mjs": typeof import("resource://tps/modules/bookmarks.sys.mjs"),
  "resource://tps/modules/formautofill.sys.mjs": typeof import("resource://tps/modules/formautofill.sys.mjs"),
  "resource://tps/modules/forms.sys.mjs": typeof import("resource://tps/modules/forms.sys.mjs"),
  "resource://tps/modules/history.sys.mjs": typeof import("resource://tps/modules/history.sys.mjs"),
  "resource://tps/modules/passwords.sys.mjs": typeof import("resource://tps/modules/passwords.sys.mjs"),
  "resource://tps/modules/prefs.sys.mjs": typeof import("resource://tps/modules/prefs.sys.mjs"),
  "resource://tps/modules/tabs.sys.mjs": typeof import("resource://tps/modules/tabs.sys.mjs"),
  "resource://tps/modules/windows.sys.mjs": typeof import("resource://tps/modules/windows.sys.mjs"),
}
