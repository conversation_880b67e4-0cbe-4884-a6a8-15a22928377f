extensions.{dc572301-7619-498c-a57d-39143191b318}.description=Permette la navigazione a schede con una marcia in più
tmp.merge.warning.title=Chiusura finestra con schede non ancora incorporate
tmp.merge.warning.message=Le schede selezionate verranno incorporate in un'altra finestra; ma quelle non selezionate verranno chiuse insieme alla finestra attuale
tmp.merge.warning.checkbox= Always warn you when closing a window with tabs that aren\'t merging.
tmp.merge.error= Debbono essere aperte almeno 2 finestre prima di e per poterle incorporare
tmp.merge.private= Impossibile incorporare una finestra di navigazione anonima con una non anonima
tmp.importPref.error1=Impossibile importare a causa del file non valido
tmp.importPref.error2=Importazione delle impostazioni non riuscita
tmp.sessionempty=Al prossimo avvio del browser l'ultima sessione sarà vuota
droptoclose.label=Rilascia una scheda per chiuderla
flstOn.label=Porta in primo piano l'ultima scheda
flstOff.label=Porta in primo piano la scheda a destra
slideshowOn.label=Rotazione delle schede avviata, premere F8 per fermarla
slideshowOff.label=Rotazione delle schede fermata, premere F8 per avviarla
undoclosetab.keepOpen.label= Keep menu open
undoclosetab.keepOpen.description= click to toggle
undoclosetab.clear.label= Svuota lista schede chiuse
undoclosetab.clear.accesskey= c
undoClosedWindows.clear.label= Svuota lista finestre chiuse
undoClosedWindows.clear.accesskey= f
protectedtabs.closeWarning.1=Si sta per chiudere %S scheda protetta.
protectedtabs.closeWarning.2=Si stanno per chiudere %S schede protette.
protectedtabs.closeWarning.3=Si stanno per chiudere %S schede aperte, di cui protette %S.
protectedtabs.closeWarning.4=Continuare?
protectedtabs.closeWarning.5=Warn you when closing window with protected tabs
window.closeWarning.2=Warn you when closing window with multiple tabs
closeWindow.label=Chiudi finestra
confirm_autoreloadPostData_title=Avviso di Tab Mix Plus
confirm_autoreloadPostData=La pagina che si sta per ricaricare contiene POSTDATA\nSe si ricarica la pagina, qualsiasi azione il modello abbia effettuato (come una ricerca o un acquisto in linea) verrà ripetuta!\n\nRicaricare comunque la pagina?
confirm_autoreloadPostData_remote=The page on which you tried to enable Auto Reload contains POSTDATA.\nIf you enable Auto Reload, any action the form carries out (such as an online purchase) will be lost.\n\nAre you sure that you want to enable Auto Reload?
incompatible.title= Avviso di Tab Mix Plus
incompatible.msg0= Le seguenti estensioni sono già incorporate oppure sono incompatibili con Tab Mix Plus
incompatible.msg1= Disattivare queste estensioni?
incompatible.msg2= Le estensioni incompatibili verranno disattivate dopo il riavvio del browser
incompatible.button0.label= Disattiva
incompatible.button0.accesskey= D
incompatible.button1.label= Non disattivare
incompatible.button1.accesskey= O
incompatible.button2.label= Disattiva e riavvia
incompatible.button2.accesskey= E
incompatible.chkbox.label= Mostra questo avviso all'avvio del browser
tabmixoption.error.title= Errore di Tab Mix Plus
tabmixoption.error.msg= È necessario avere una finestra del browser aperta per utilizzare le impostazioni di Tab Mix Plus
# LOCALIZATION NOTE (rowsTooltip.rowscount):
# Semicolon-separated list of plural forms. See:
# http://developer.mozilla.org/en/docs/Localization_and_Plurals
# #1 is the total number of rows
# The singular form is not considered since this string is used only for
# multiple rows.
rowsTooltip.rowscount=;#1 Rows
rowsTooltip.activetab=Active tab on row #1
bugReport.label=Report a Bug
