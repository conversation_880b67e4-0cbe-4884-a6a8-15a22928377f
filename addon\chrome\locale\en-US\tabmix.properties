extensions.{dc572301-7619-498c-a57d-39143191b318}.description= Tab browsing with an added boost.
tmp.merge.warning.title= Closing window with non-merged tabs
tmp.merge.warning.message= The selected tabs will be merged with another window, but the other tabs will now be closed with the current window.
tmp.merge.warning.checkbox= Always warn you when closing a window with tabs that aren\'t merging.
tmp.merge.error= At least 2 windows have to be opened before you can merge them
tmp.merge.private= You can not merge private window with non-private window.
tmp.importPref.error1= Can not import because it is not a valid file.
tmp.importPref.error2= Failed to import settings.
tmp.sessionempty= Next time you start the browser, \'Last Session\' will be empty.
droptoclose.label= Drop a tab to close it
flstOn.label= Tabs Focus to Last Selected - Press F9 to swap preference
flstOff.label= Tabs Focus to Right - Press F9 to swap preference
slideshowOn.label= Tab Rotation is On - Press F8 to turn off
slideshowOff.label= Tab Rotation Off - Press F8 to turn on
undoclosetab.keepOpen.label= Keep menu open
undoclosetab.keepOpen.description= click to toggle
undoclosetab.clear.label= Clear Closed Tabs List
undoclosetab.clear.accesskey= C
undoClosedWindows.clear.label= Clear Closed Windows List
undoClosedWindows.clear.accesskey= C
protectedtabs.closeWarning.1=You are about to close %S protected tab.
protectedtabs.closeWarning.2=You are about to close %S protected tabs.
protectedtabs.closeWarning.3=You are about to close %S tabs, %S of them protected.
protectedtabs.closeWarning.4=Are you sure you want to continue?
protectedtabs.closeWarning.5=Warn you when closing window with protected tabs
window.closeWarning.2=Warn you when closing window with multiple tabs
closeWindow.label=Close window
confirm_autoreloadPostData_title=Warning!
confirm_autoreloadPostData=The page on which you tried to enable Auto Reload contains POSTDATA.\nIf you enable Auto Reload, any action the form carries out (such as an online purchase) will be repeated.\n\nAre you sure that you want to enable Auto Reload?
confirm_autoreloadPostData_remote=The page on which you tried to enable Auto Reload contains POSTDATA.\nIf you enable Auto Reload, any action the form carries out (such as an online purchase) will be lost.\n\nAre you sure that you want to enable Auto Reload?
incompatible.title= Tab Mix Plus
incompatible.msg0= The following extensions are integrated or incompatible with Tab Mix Plus.
incompatible.msg1= Would you like to disable these extensions?
incompatible.msg2= The incompatible extensions will be disabled after you restart your browser.
incompatible.button0.label= Disable
incompatible.button0.accesskey= D
incompatible.button1.label= Don\'t Disable
incompatible.button1.accesskey= O
incompatible.button2.label= Disable and Restart
incompatible.button2.accesskey= E
incompatible.chkbox.label= Show this alert when browser starts
tabmixoption.error.title= Tab Mix Plus Error
tabmixoption.error.msg= You must have one browser window to use Tab Mix Plus Options
# LOCALIZATION NOTE (rowsTooltip.rowscount):
# Semicolon-separated list of plural forms. See:
# http://developer.mozilla.org/en/docs/Localization_and_Plurals
# #1 is the total number of rows
# The singular form is not considered since this string is used only for
# multiple rows.
rowsTooltip.rowscount=;#1 Rows
rowsTooltip.activetab=Active tab on row #1
bugReport.label=Report a Bug
