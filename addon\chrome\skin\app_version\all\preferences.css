/**
    Firefox all versions

    All platform

**/

/* toolkit/content/xul.css */

/********** preferences ********/

prefwindow,
prefwindow:root /* override :root from above */,
prefpane {
  flex-direction: column;
}

prefwindow > .paneDeckContainer {
  overflow-x: hidden;
}

prefpane > .content-box {
  overflow: hidden;
}

prefwindow[type="child"] > .paneDeckContainer {
  overflow: clip;
}

prefwindow[type="child"] > prefpane > .content-box {
  flex: 1;
  overflow: clip;
}

preferences,
preference {
  visibility: collapse;
}

radio[pane] .radio-check {
  display: none;
}

radio[pane] .radio-label-box {
  flex-direction: column;
  outline: none;
  margin-inline: 5px;
  margin-bottom: 2px;
}

radio[pane] .radio-icon {
  margin-inline-end: 0;
}

radio[pane] .radio-label {
  margin-top: 1px;
}

prefwindow[chromehidden~="toolbar"] .chromeclass-toolbar {
  display: none;
}

.button-menu-dropmarker {
  direction: rtl;
  appearance: auto !important;
}

.button-menu-dropmarker .button-icon {
  margin-left: 5px;
}

windowdragbox {
  width: 100%;
}

.font-bold {
  font-weight: bold;
}
