Tab Mix Plus License and Payment Notice
======================================

Tab Mix Plus is open source software, released under the Mozilla Public License, version 2.0 (see below).

**Sustainable Development Notice (Effective with version 1.32):**
To support ongoing development and compatibility updates, we kindly request that users
of Tab Mix Plus version 1.32 and newer contribute a monthly payment of $2 (or $1 if needed).
This is a voluntary "Trust Plan"—there are no technical or legal restrictions on use,
and the extension remains open source.
Your support ensures continued improvements and compatibility with new Firefox versions.

- Versions 1.31 and earlier remain free and are compatible with Firefox up to version 139.
- For more information and payment instructions, see: https://onemen.github.io/tabmixplus-docs/update/

Thank you for supporting Tab Mix Plus!

---
The following license applies to all files:

/* ***** BEGIN LICENSE BLOCK *****
 *
 * The contents of this file are subject to the Mozilla Public License
 * Version 2.0 (the "License"); you may not use this file except in
 * compliance with the License. You may obtain a copy of the License at
 * http://mozilla.org/MPL/2.0/
 *
 * Software distributed under the License is distributed on an "AS IS"
 * basis, WITHOUT WARRANTY OF ANY KIND, either express or implied. See the
 * License for the specific language governing rights and limitations
 * under the License.
 *
 * The Original Code is the Tab Mix Plus extension (https://onemen.github.io/tabmixplus-docs).
 *
 * The Initial Developer of the Original Code is ONEMEN (<EMAIL>).
 * Portions created by Initial Developer are Copyright (C) 2009
 * to the Initial Developer. All Rights Reserved.
 *
 * Contributor(s):
 * * Gary Reyes [CPU].
 * * SUN Chun-Yen.
 * *
 * * 117649 <<EMAIL>>
 * * Timothy Humphrey - (Original Author for lasttab.js)
 * * Piotr Sielski - graphics.
 *   &
 * - BabelZilla translators:
 * * kompix [ca-AD]"
 * * Alena Jaֵ¡provֳ¡ [cs-CZ]"
 * * AlleyKat [da-DK]"
 * * ReinekeFux [de-DE]"
 * * Juan Guillermo Gamba [es-ES]"
 * * urko [es-ES]"
 * * Pierre [fr-FR]"
 * * Ptit Lutin [fr-FR]"
 * * Tibox [fr-FR]"
 * * moZes [fy-NL]"
 * * sipster [fy-NL]"
 * * truijentink [fy-NL]"
 * * KAMI [hu-HU]"
 * * Luana Di Muzio [it-IT]"
 * * Shadow912 [ja]"
 * * Flactal [ko-KR]"
 * * Jonas [lt-LT]"
 * * Alf, markh and Juliette [nl-NL]"
 * * Leszek(teo)Zyczkowski [pl-PL]"
 * * Pardal Freudenthal [pt-BR]"
 * * teboga [pt-BR]"
 * * ultravioletu [ro-RO]"
 * * ArtLonger [ru-RU]"
 * * Jacen [sk-SK]"
 * * Aycan Demirel [tr-TR]"
 * * ErkanKaplan [tr-TR]"
 * * Fatih [tr-TR]"
 * * fang5566 [zh-CN]"
 * * yuoo2k [zh-TW]"
 *
 * Alternatively, the contents of this file may be used under the terms of
 * either the GNU General Public License Version 2 or later (the "GPL"), or
 * the GNU Lesser General Public License Version 2.1 or later (the "LGPL"),
 * in which case the provisions of the GPL or the LGPL are applicable instead
 * of those above. If you wish to allow use of your version of this file only
 * under the terms of either the GPL or the LGPL, and not to allow others to
 * use your version of this file under the terms of the MPL, indicate your
 * decision by deleting the provisions above and replace them with the notice
 * and other provisions required by the GPL or the LGPL. If you do not delete
 * the provisions above, a recipient may use your version of this file under
 * the terms of any one of the MPL, the GPL or the LGPL.
 *
 * ***** END LICENSE BLOCK ***** */
