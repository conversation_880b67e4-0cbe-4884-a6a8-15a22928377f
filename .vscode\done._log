--------------------------------------------------------------------------------------------------
done 2024-10-18: d22012ffd0ce56e01267edcbe050267bf8aa4e8f
2024-07-13
check what are versions flag we have and if i can drop support for old firefox versions
78-114  - 119 version\((7[8-9]|[8-9][0-9]|10[0-9]|11[0-5])[0-9]\)
115-130 -  70 version\((11[5-9]0|1[2-9][0-9][0-9])\)

V check ChromeUtils.jsm, maybe i can replace .jsm with sys.mjs for all files that are ready for Firefox 115
  V "resource://gre/modules/PromiseUtils.jsm": does not exist in nightly, we use it in MergeWindows.jsm
V fix useage of XPCOMUtils and Services
V remove all Services.jsm imports
V remove updateBeforeAndAfter
V remove deprecated .d.ts type that are not in use
V check preferences xhtml file and make sure to remove obsolete prefs and dom elements
V Firefox forks
  V identify firefox version for each for version
    Waterfox G6.0.20 - "115.17.0"
    LibreWolf use same version numbers as firefox
    floorp 11.19.1 - "128.4.0", currently not supported
  V waterfox: remove compatibility for older version (isG3Waterfox, isG5Waterfox: 10 places), support only version G6 (base on ESR 115)
V update also css files and remove old files
V check @ts-expect-error and @ts-ignore
V check comment in css related to firefox version
V update Tabmix.tablib.showClosingTabsPrompt, not to repeat calls to formatValueSync
  check if we need this function at all
V update use of TabmixSvc.i10IdMap - skip the check if array is empty
V Bug 1649221 Update ChromeUtils.generateQI callers to pass strings rather than interface objects
V check if `Tabmix:sendDOMTitleChanged` is still in use - REMOVED
V check if `Tabmix:updateHistoryTitle` is still in use
V check if `Tabmix:collectReloadData` is still in use
V bug 1662410 - Remove usage of ChildSHistory::LegacySHistory - clean content.js legacySHistory
V check if wen need Tabmix_NewTabURL  NewTabURL.jsm - it contain observer for newTabURL
--------------------------------------------------------------------------------------------------
2024-08-01
 try - ThisType<Type> https://www.typescriptlang.org/docs/handbook/utility-types.html#thistypetype
 read more about typescript reference: https://www.typescriptlang.org/docs/handbook/project-references.html

 V merge types form lib.gecko.services.d with gecko.d.ts
 V merge types form lib.gecko.xpcom.d with gecko.d.ts
 - search for unused /** @type */ comments
 V try to replace @ts-ignore with @ts-expect-error
 - convert to .sys.mjs
 V add script to import gecko .d.ts files from github or mozilla central
 - consider converting my eslint plugin to typescript
 - investigate more about class types, and how to type classes in Javascript using .d.ts
 V apply "strictNullChecks", "exactOptionalPropertyTypes", "strictPropertyInitialization"
 V check if i need to use separeted type scope for bootstrap.js
 V find a way to override Services form gecko.d.ts and nsIPrefBranch
 V find a way to get type for ChromeUtils.defineModuleGetter, defineLazyModuleGetters, import...
    V add style to TabmixChromeUtils.import to it the same as in gecko.d.ts
    V add types for other Firefox files that we import with ChromeUtils.importESModule or ChromeUtils.defineModuleGetter
    V add types to `ChromeUtils.import` for our jsm files (also other imports):
      "chrome://tabmix-resource/content/Places.jsm"
    V add types to other imports for Firefox files
 V add types to all imports `interface PlacesUIUtils {}`
 V use unknown instead of any, extraTabmixUtils.d.ts some any?
 V add types to TabmixSvc.ss
 V add typed map for getElementsByTagName
 V remove Partial from our modules types, add missing entry or @ts-ignore
 V check all TODO's in .d.ts files
 V check again all any in .d.ts files
 V apply strict mode ??
 V add interfaces to all object from firefox and Tabmix
 ? improve the type of `HTMLElement & HTMLInputElement & XULTab`
 V add type for `document.createXULElement`
 V remove tacofig.ts files from xpi - on github action
 V move namespaces from addon.d.ts to extraTabmixUtils and fix all any's

 V check .d.ts file and fix some errors
 V update ignore file - remove .github
 V split type for addon\chrome\content\scripts
 V add types for all inner functions
 V apply noImplicitAny
 V remove tacofig.ts files from xpi - on build scrips
 V update click.js types in addon.d.ts
 X check if i can use interface with the same name as the class
 V move Tabmix module that are from utils.js to seperated file, it is used by both addon and preferences

--------------------------------------------------------------------------------------------------
2024-07-08
 V modify `Recently closed tabs` and `Recently closed windows` in the right appmenu
 V check if i can add keep menu open also to appmenu view
 V add workaround for bug 1868452 Key key_undoCloseTab of menuitem could not be found
 V add style to the menu item that triggered the context menu
--------------------------------------------------------------------------------------------------
2024-07-11
manually init sidebar it it was not started before

check if SidebarController._sidebars.has("viewCustomizeSidebar")

```js
// need also to remove this listeners
this._switcherCloseButton = document.getElementById("sidebar-close");
this._switcherCloseButton.addEventListener("command", () => {
  this.hide();
});
this._switcherTarget.addEventListener("command", () => {
  this.toggleSwitcherPanel();
});
this._switcherTarget.addEventListener("keydown", event => {
  this.handleKeydown(event);
});
```

```js
if (SidebarController.sidebarRevampEnabled && !SidebarController._sidebars.has("viewCustomizeSidebar")) {
  SidebarController.uninit();
  SidebarController._sidebars = null;
  void SidebarController.sidebars;
  SidebarController._inited = false;
  SidebarController._initDeferred = Promise.withResolvers();
  SidebarController.init();
}
```
--------------------------------------------------------------------------------------------------
2024-07-10
## prepare for vertical tabs sidebar

to enable it set the preferences:
 sidebar.revamp: true (default: false)
 sidebar.verticalTabs: true (default: false)
 sidebar.visibility: always-show (default: always-show)

it is better to turn this pref off for vertical:
 browser.tabs.hoverPreview.enabled

 bugs:
 - check if we need to use "DOMMouseScroll"
 V scroll all tabs does not work when tabmix enabled:  need to update the code to scroll the side bar
 V check prefs to position buttons on the Tab Bar:
     on vertical mode Firefox show new-tab-button on both places
     when in vertical mode hide the ('new-tab-button') ,list in the options (and set the ui to right side)
     set the 'tabs-newtab-button' to be always visible
 V our background colors does not apply
 - #tabbrowser-tabs is not inside #TabsToolbar>#TabsToolbar-customization-target
   check all styles in our code (css files and tab.js)
 V update style for new-tab-button on the sidebar (maybe its a Firefox issue)
 V update comment about vertical tabs in our preferences window
 V tabclick options on verticalTab sidebar does not work, maybe i need to add listener on another target
 V fix option to show extra spacees on both side
 V update usage of Tabmix.extensions.verticalTabs, Tabmix.extensions.verticalTabBar.
 - add sidebar preference to our options window
 - check if i need to add 'tabmix-tabs-closebutton' to CustomizableUI.AREA_TABSTRIP
--------------------------------------------------------------------------------------------------
2024-07-07 - patch is ready, still testing
use @stylistic/eslint-plugin-js: https://eslint.style/guide/migration
--------------------------------------------------------------------------------------------------
 work on compatibility with Waterfox lepton theme
 - multirow not working with waterfox themes (Lepton, Australis)
--------------------------------------------------------------------------------------------------
[+] create new script to check for all the resources that we use if .sys.mjs exists
[+] add rule to it in eslint-tabmix-plugin, if .sys.mjs exists we must use TabmixChromeUtils
[+] and make sure that the list in Tabmix ChromeUtils.jsm includes the modules that we use
[+] need to modify eslint-plugin-mozilla to support TabmixChromeUtils.defineLazyModuleGetters

Bug 1308512 (esm-ification) [meta] - Migrate from ChromeUtils.import to ES6 Modules
Bug 1779984                 [meta] - Migrate from ChromeUtils.import to ES6 Modules


[+] Bug 1779621 - Migrate intl/locale from jsm to esm
  - this bug landed on Firefox 105


[+] Bug 1779982 - Migrate places from ChromeUtils.import to ES6 Modules
   - PlacesUIUtils.sys.mjs
   - PlacesUtils.sys.mjs


[+] Bug 1777486 - Migrate XPCOMUtils.jsm
  need to update our code to use
  const { XPCOMUtils } = ChromeUtils.importESModule("resource://gre/modules/XPCOMUtils.sys.mjs");
  instead of
  const { XPCOMUtils } = ChromeUtils.import("resource://gre/modules/XPCOMUtils.jsm");
  for Firefox 104+

[+] updated eslint-tabmix-plugin to prevent using Cu.import on files that have .sys.mjs extension

--------------------------------------------------------------------------------------------------
2024-10-20
 V need to update compatibility with floorp
 V TMP_tabDNDObserver_init was unable to change gBrowser.tabContainer._animateTabMove
 V update vertical margin/padding for multi-row
 V update hover state on close tab buttons
 V fix floorp new tab button position, it jump to next row and sometimes its flicker
 V with my setting and `width fill row` - new tab button - repeatedly jump for right-side to after tabs
 V new tab button can be on 2nd row without a tab
 V extensions.tabmix.flexTabs_fitRow only applied after i toggle it once
 X i can not reproduce this:
   when setting scrollbox without buttons tabbar height toggle +-1-2px, probably by our calculations of new tab button
   it does not need to do anything when it is not multi-row
 V reload tab every - customize - input width is too small
 V Firefox Photon・Lepton UI: need to set `userChrome.padding.tabbar_height` to false
 V drag tabs in vertical mode not working, the dragged tab is visible but other tabs disappear as the dragged tab pass them
--------------------------------------------------------------------------------------------------
2024-10-17
 V use global.es2024
 V use @stylistic/eslint-plugin-js
 V clean our config, add only rules that are different from mozilla or eslint recommended
 V remove workaround for eslint-plugin-json and eslint-plugin-no-unsanitized/lib/ruleHelper
 V remove rules that are off by default and not exist in recommended
 V group @stylistic rules
--------------------------------------------------------------------------------------------------
2024-10-25
  - fix firefox-changes\addon\chrome\content\browserCss.js after changes in chrome://browser/skin/tabbrowser/tabs.css
    Bug 1924534 - add fade out effect back to vertical tabs
--------------------------------------------------------------------------------------------------
2024-10-25
 V update code after new private methods in tabs.js after bug 1926582
 V when dragging multiple tabs wee need to fix _animateTabMove, somthing is wrong with Tabmix.getMovingTabsWidth
   the background tab does not move when the dragging tab reach its mid point
--------------------------------------------------------------------------------------------------
2024-11-08
replace `window.performance.timing.navigationStart` with window.performance.timeOrigin
MDN Reference](https://developer.mozilla.org/docs/Web/API/Performance/timing)
see typescript/lib/lib.dom.d.ts

 parseInt(window.performance.timeOrigin)===window.performance.timing.navigationStart
--------------------------------------------------------------------------------------------------
2024-11-07
fix error in the docs https://github.com/onemen/TabMixPlus/issues/356
i have tested it on ubuntu and mint without any issue
--------------------------------------------------------------------------------------------------
2024-11-13
 Bug 1929898 - Need to add a pause on drag and drop to create tab group
 Implement replacement for private methods that we use in gBrowser.tabContainer._animateTabMove
  - #clearDragOverCreateGroupTimer
  - #dragOverCreateGroupTimer
  - #triggerDragOverCreateGroup
--------------------------------------------------------------------------------------------------
2024-11-12
 fix style of selected panel in tabmix options on linux
--------------------------------------------------------------------------------------------------
2024-11-12
add Services.appinfo.invalidateCachesOnRestart() to bootstrap
read more here: https://github.com/onemen/TabMixPlus/issues/359
--------------------------------------------------------------------------------------------------
preferences style:
  - waterfox: dispaly tab-bar position select width is wrong
--------------------------------------------------------------------------------------------------
2024-10-15
check how to improve tabs styles - background colors, gradients ...
  X update our tab color style to use shadow instead of bottom border ?
  V remove old styles for background - tab-background-middle
  V can not see hover effect over close tab button with some custom colors
  V close buttom on selected tab moves on hover, with widthFitTitle
  ? check how other button on tabs look in deferrent background colors
  ? add option to show shadow on tabs in our dynamic css ???
--------------------------------------------------------------------------------------------------
2024-11-26
 Bug 1927774: Add closed tab groups to history menus
 check what i need to update in tabmix
 V closed tabs list button - menu missing seperatore at the button
 V history closed tabs list button - i can add max-height: 700px to 'Recently Closed Tabs' menupopup
   I did it in my userChrome.css
 V {id: "tabbrowser-confirm-close-tabs-checkbox"} changed
 V check how closed group need to appear in the menu and if it is working in my code.
 V closed tabs list button on new window does not enabled event when the list show closed tabs from other windows
   check when it was changed - check how it work without Tab Mix
 V check how to apply our closed tabs context menu to closed group menupopup
 V test all close tabs list context menu functions....
 V implement closed group from tab/main contextmenu
    V restore tabs
    V cleat group
    V delete tab with middle click
 X context menu does not work on closed tabs list from tab/main contextmenu
 - check if it is possible to show closed tabs in private windows (what Firefox do?)
 V add ability to remove closed tabs group with middle click - i added 'Clear tab group' to group sub menu
 V add option to tab group sub menu to `Clear tab group`
 V sometimes i can not delete closed tabs by middle click when the are also closed group
 V check restore all tabs in firefox, does it reopen closed group or just restore the tabs without the group
 V need to fix `appMenu-library-recentlyClosedTabs` when keepMenuOpen is on, calling updateAppmenuView add 2nd 'Reopen All Tabs'
 V clean TODO: and remove all commeneted code
--------------------------------------------------------------------------------------------------
2024-12-27
 fix compatibility with Zen Browser
 V fix all usage of `Services.appinfo.version` need to use Services.appinfo.platformVersion
 V fix chrome.manifest appversion use `platformversion` instead of `appversion`
 V add check for buttons in the toolbar that are removable in Zen, "new-tab-button", "alltabs-button"
   - update code to check if buttons move back to the toolbar after customization
     need to toggle init/deinit for the buttons
 V fix `Key key_undoCloseTab of menuitem null could not be found` errors
 V fix styles from option window
   V current pane title color have low opacity
   V inputs and button background color is transparent
   V compare how it look in Firefox 135
 V fix error notification in about:addons
 V bootstrap does not show warning to restart the browser when the extension disabled by the user in about:addons
 - check compatibility with vertical tabs, make sure multirow is off
 V check if autoupdate work when the update `"gecko": {"strict_min_version": "115"}`
   try to change update url dinamically?
--------------------------------------------------------------------------------------------------
2025-01-16
 - need to remove all `-moz-lwtheme` and use `:root[lwtheme]` since Firefox 126
 Bug 1884792 - Remove chrome-only :-moz-lwtheme pseudo-class

 this bug was since Firefox 100
 Bug 1760342 - Remove moz-lwtheme-{brighttext,darktext}

 need to remove `-moz-platform: windows-win7` `-moz-platform: windows-win8`
 remove in firefox 117
 Bug 1843663 - Remove pre-win-10/11 media queries and styles.

 check when `-moz-windows-compositor` removed or replaced

refactor: update themes compatibility

 - new preference to set theme background image height options are repeat or cover
 - followup bug 1884792 - Remove chrome-only :-moz-lwtheme pseudo-class
 - followup bug 1843663 - Remove pre-win-10/11 media queries and styles
 - followup bug 1760342 - Remove moz-lwtheme-{brighttext,darktext}
--------------------------------------------------------------------------------------------------
2024-11-04
 - try to set close button on tabs to be invers color of custom background color
 - also update hover colors
--------------------------------------------------------------------------------------------------
 2025-02-03
 check Zen browser
 V Show Tab close button on all tabs
   https://github.com/onemen/TabMixPlus/issues/371#issuecomment-2631430096

 V TMP_EL_onWindowOpen was unable to change gBrowser.tabContainer._updateCloseButtons
 V don't disable hide the tab bar in tabmix options
 check https://zen-browser.app/mods/
--------------------------------------------------------------------------------------------------
 2025-01-25
  move `sorted tab` menu item above tab group in All tabs popup
--------------------------------------------------------------------------------------------------
2025-02-06
  check how to set file ext to txt when saving preferences in mac
--------------------------------------------------------------------------------------------------
2025-01-19
fixed - https://github.com/onemen/TabMixPlus/commit/7d450f14dfc32eab8d5847f2cb1ece106d4ddfd5
fix issue 376
 - need to fix this logic when dragging pinned tab
 - need to handle the case when user drag link to the pinned area
 - need to handle the case when user drag tab to the pinned area
 - check how firefox handle dragging tab to the pinned tab area and out of it

 - when dragging normal tab to pinned tab area show drop indicator before the first non pinned tab
 - when dragging pinned tab out of the pinned tab area show drop indicator after the last pinned tab
 - when dropping link to pinned tab area open the tab as the first non-pinned tab

 /**
 * when dragging link or normal tab to pinned tab area show drop indicator
 * before the first non pinned tab.
 * when dragging pinned tab out of the pinned tab area show drop indicator
 * after the last pinned tab
 */
--------------------------------------------------------------------------------------------------
2025-01-24
 Bug 1881888 - Remove support for JSMs  - check if we need to handle saved closed group in Tabmix
     Bug 1936914 - Add saved tab groups to "Recently closed tabs" menus
  - complete all TODO's the patch and commented code
  - need to update types for files in modules
  V ChromeUtils.defineModuleGetter removed
  V ChromeUtils.import removed
  V Cu.import removed
  V update TMP_ClosedTabs.undoCloseTab after latest changes in Firefox
    Bug 1940752 - 'Reopen Closed Tab' tab context menu, and other paths calling undoCloseTab, support tab groups
  - Bug 1945238 - SessionStore should expose function(s) to forget single closed tab from closed/saved group
  V Bug 1932941 - Session support for closed tab groups in closed windows
  V Bug 1943850 - Recently closed tabs use correct indexing (fixed regressions from 1932941)
  - Bug 1944416 - Individual tabs from closed tab groups in closed windows cannot be restored from history menus
  - Bug 1944328 - "Reopen All Tabs" should support closed tab groups from closed windows
  V test closed group without name in Firefox 135, 136
  V when the pref to list closed tabs from closed windows is off
    Tabmix button state does not update when closed windows is open
    it is only update when window is closed
  V in Firefox 135 sometime Tabmix closed button is enabled but list is empty
  V remove use-mjs-modules.js
  V update valid-lazy.js
  ? modify TabmixChromeUtils, ChromeUtils.sys.mjs to get .sys.mjs and check for .jsm
    we can keep it until we drop support firefox version before 128

  refactor - remove obsolete code
  V remove TabRestoreQueue.sys.mjs and related preferences and code
  V remove session.js and all references to it, TabmixSessionManager, TabmixSessionData
  V remove Tabmix.Sanitizer code and UI
  V remove tabView.js and all references to it TMP_TabView
  V remove TabGroupManager.sys.mjs and all references to it
  V remove Decode.sys.mjs and all references to it
  V clean backwardCompatibilityGetter from session.js
  V clean constant from session.js
  V clean AddonManager.sys.mjs
  V clean preferences UI code and preferences from addon\defaults\preferences\tabmix.js
  V clean TabmixSvc.sm
  V remove references to check if i need to monitor about Tabmix.extensions.sessionManager extension
  V remove any reference or comment about sessionManager only refer to SessionStore if needed
  V check what `tm_sessionmanagerContextMenu` do?
  V tmp_sessionmanagerButton
  V updateSessionShortcuts
  V clean Shortcuts.sys.mjs
  - remove unused css rules
  V clean unused locals from pref-tabmix.dtd
  V remove unused locals session-manager.properties
  V replace Tabmix.promptService with build in service
  V remove useage of Tabmix.promptService and all related code

  V we can NOT use SessionStoreInternal from SessionStore.sys.mjs, need to update all references to it in Tabmix
  V we can NOT use TabmixSvc.SessionStoreGlobal" from SessionStore.sys.mjs
  X check if i can use getInternalObjectState(obj) to modify internal sate and use it to remove closed tabs?
    can not use it for closedwindows
--------------------------------------------------------------------------------------------------
2024-10-19
  prepare for using only SessionStore.sys.mjs after SessionStore.jsm will will be not accessible
  - replace all useage of TabmixSvc.SessionStore
  - remove use of our TabRestoreQueue
  - check again place.js for restore on demand
--------------------------------------------------------------------------------------------------
2024-10-19
 - convert all .jsm to .sys.mjs modules
--------------------------------------------------------------------------------------------------
look at comment
https://github.com/onemen/TabMixPlus/issues/375#issuecomment-2667686377

 V when the dragged tab is pinned but part of selected tabs that have some non pinned tabs
   Prevent animation when grouping selected tabs
 V in multi-row selected tabs does not grouped together if some are pinned tabs
 V fix drop indicator position the dragging multiple tabs, make it work the same as Firefox do

 V after dragging pinned tabs when there are multi-rows in overflow, the pinned tabs lost the position fixed
   attribute, check the changed i have made recently

 V can not drag pinned tabs when multi-row are not scrolled to the top
--------------------------------------------------------------------------------------------------
2025-02-16
 Multi-rows drag and drop multi-selected tabs #375
  - check how firefox reorder multi-selected tabs when drag starts
    need to fix the case user select non consecutive tabs in multi-row
  - fix drop index to be before or after the group
  - test it RTL
--------------------------------------------------------------------------------------------------
2025-02-05
 Bug 1875216 - part 2: Remove linting for XPCOMUtils.defineLazyGetter remove XPCOMUtils from valid-lazy.js
 update eslint-plugin-mozilla
--------------------------------------------------------------------------------------------------
2025-01-22
 - in multi-row, clicking on scroll-down button scroll number of rows every click (the same as visible rows),
   clicking on scroll-up scroll one row
   ths issue is in `scrollByIndex(index, aInstant) {`
   ```
   var x = index > 0 ? rect[end] + 1 : rect[start] - 1;
   ```
   need to replace the value of rect[end] with rect[start] + singleRowHeight
 - check how `gBrowser.tabContainer.arrowScrollbox._startScroll work without smoothscroll
   maybe i need to modify it for multi-row
 - check if function that we modified have changed in gBrowser.tabContainer.arrowScrollbox
--------------------------------------------------------------------------------------------------
2025-02-21
 - working on #379:
   When overflowing, dragging the tab to the top/bottom edges should make the tabs scroll #379

 - need to improve scrolling when smoothscroll is off
 - check how Merci-chao do the scroll, and how it work with smoothscroll off
 - check how to use finishScroll
 - add UI to turn this feature on/off
 - check why scrollByPixels feel sluggish when windows is not maximized

 - how to handel scroll when dragging link
 - how to prevent scroll when dragging tab to detach it
--------------------------------------------------------------------------------------------------
2025-02-11
 docs update:
  V add `Theme background style` to multi-rows sections
  V add `Scroll tabs when dragged to top/bottom` to multi-rows sections
  V update links to firefox scripts to release page in tabmixplus
  V add Zen Browser compatibility
  V improve docs and github readme about development build - it can be use in all versions from 115 ESR to latest Firefox nightly
    add comment in dev-build release page.
  V update compatibility section in TabmixPlus README.md
  V update page about new version add amount for donation
  V update version-update page to show important link or content at the top left, maybe always to show change log?
  V add text about 'tested on Firefox xxx' or 'ready for Firefox xxx'
  - maybe add new menu entry for `New in Tab Mix`

  X check kent live stream about image optimization
  V improve special sidebar link to opened in the same page with transion
    V http://localhost:4321/tabmixplus-docs/help/links#file-type-editor
    V http://localhost:4321/tabmixplus-docs/help/display-tab#customize-styles
--------------------------------------------------------------------------------------------------
2025-02-24
 issue with zen 1.8.1 https://github.com/onemen/TabMixPlus/issues/411
  X instead of `for (const tab of tabs.filter(t => t.tagName === "tab")) {`
    maybe it is better to check if tab.linkedBrowser exist ?
  V check how gBrowser._beginRemoveTab work when closing last tab
    check if i need to modify `ZenWorkspaces.selectEmptyTab();` instead of gBrowser._beginRemoveTab
  V add easy way to start tabmix options in Zen
  V update tab custom colors, check dynamic styles
--------------------------------------------------------------------------------------------------
2025-01-21
I Can not longer reproduce any of this issus - tested on 2025-02-28
 bugs in Firefox Hebrew RTL
 - scrolling multi-row tabs that have pinned tabs
   when scrolling to the last row, first tab in row for the previous row does not calcults properly
 - multi-row flicker when new tab button is after last tab

 // there is bug in firefox when swapping margin for RTL in tabContainer.on_dragover
 // TODO - fill a bug in bugzilla, try to identify when this bug started
 // need to check if this still an issue in nightly
 /\[minMargin, maxMargin\]\s=[^;]*;/,
 '[minMargin, maxMargin] = [minMargin, maxMargin]'
--------------------------------------------------------------------------------------------------
2025-03-02
  https://github.com/onemen/TabMixPlus/issues/413
- Open links from other applications in:
  New window - does nothing.
  New tab - opens in a new tab.
  Current tab - opens in a new window.

'Open links from other applications in: New window' -> does nothing
Error in Firefox 135
ReferenceError: openDialog is not defined
    getContentWindowOrOpenURI chrome://tabmix-resource/content/BrowserDOMWindow.sys.mjs line 99 > eval line 9 > eval:101
    openURI chrome://browser/content/browser.js:4227
    openInWindow resource:///modules/BrowserContentHandler.sys.mjs:1220
    handURIToExistingBrowser resource:///modules/BrowserContentHandler.sys.mjs:1238
    dch_handle resource:///modules/BrowserContentHandler.sys.mjs:1601
BrowserDOMWindow.sys.mjs line 99 > eval line 9 > eval:126:19

'Open links from other applications in: Current tab' -> opens in a new window
Error in Firefox 135
Uncaught TypeError: Tabmix.whereToOpen is not a function
    getContentWindowOrOpenURI chrome://tabmix-resource/content/BrowserDOMWindow.sys.mjs line 99 > eval line 9 > eval:79
    openURI chrome://browser/content/browser.js:4227
    openInWindow resource:///modules/BrowserContentHandler.sys.mjs:1220
    handURIToExistingBrowser resource:///modules/BrowserContentHandler.sys.mjs:1238
    dch_handle resource:///modules/BrowserContentHandler.sys.mjs:1601
BrowserDOMWindow.sys.mjs line 99 > eval line 9 > eval:79:34

Error in Nightly
TypeError: Tabmix.whereToOpen is not a function BrowserDOMWindow.sys.mjs line 92 > eval line 9 > eval:87:34
  getContentWindowOrOpenURI chrome://tabmix-resource/content/BrowserDOMWindow.sys.mjs line 92 > eval line 9 > eval:87
  openURI resource:///modules/BrowserDOMWindow.sys.mjs:154
  openInWindow resource:///modules/BrowserContentHandler.sys.mjs:1212
  handURIToExistingBrowser resource:///modules/BrowserContentHandler.sys.mjs:1230
  dch_handle resource:///modules/BrowserContentHandler.sys.mjs:1594
--------------------------------------------------------------------------------------------------
2025-02-15
  - complete TODO's in .d.ts files
  - convert some namespaces to interface
  - need to fix references order in content.js
  - need to fix circular dependencies between d.ts files
  - maybe split modules.d.ts to Firefox modules and Tabmix modules
  - refactore our types, check that each .d.ts type have no errors when open alone (need to have all its references file)
    maybe i need to split general.d.ts move services mock out ot it??
  - add types to tabmix modules
    - generate .d.ts files for all modules first
    - look for more .d.ts files in mozilla, search files that are used by chrome/browser/content/browser/tabbrowser
  - make modules.d.ts the source, and not dependent on other files
    move to it all related types
  - clean duplication in our types for Firefox imports....
  - clean namespace inside namespace in modules.d.ts ///?
  - Improve tsconfig
    https://2ality.com/2025/01/tsconfig-json.html

   add script to use `"skipLibCheck": true` by default and only set it to false when .d.ts files where changed

  // for the moment there is no benefit of changing this
  // most of our files are not ES modules
  /* AI answer:
   Based on your project structure where only files ending with .sys.mjs are ES modules,
   and the rest are CommonJS modules, I recommend keeping your current configuration:

   This is the correct setting for your project because:

   Most of your codebase appears to use CommonJS modules
   Only specific files with .sys.mjs extension use ES modules
   Firefox extensions traditionally use CommonJS-style modules
   If you need to work with both module systems, you can keep the main configuration as CommonJS and handle the ES modules separately in your build process or with specific import/export statements.

   For the .sys.mjs files, you can use import/export syntax directly, and your build system should handle them appropriately even with the CommonJS setting in TypeScript

  */


  update
   `
   "target": "ES2024",
   "lib": ["DOM", "ES2024"],
   "module": "ESNext",
   `
--------------------------------------------------------------------------------------------------
2025-03-15
  V newIndex functions are buggy  when group exist
  V need to fix dropping group-label after collapsed group
  V handle adding tab to group when dragging in multi-row
    fix when dragging tab_tPos = x right and its next sibling is in group
    allow the drop to be after the group-label and it the tab to the group

  V rename args in dragging functions draggedTab -> draggedElement
  V maybe i need to not collapsing tab group when on multi-row
    maybe i have to modify startTabDrag to block it
  V need to fix index of dragged element when dragging group-label
    test the effect of more then one group on the index
    use moveTabsBefore/moveTabsAfter
  V don't show drop indicator in the group when dragging group-label
    the same as dragging multiple tabs !!!
  V add functions for versions before 138:
     gBrowser.isTabGroupLabel = () => false
     gBrowser.isTab = () => false
  V when dragging tab to position after the group-label:
    - dragging back - add the tab to the group - OK
    - dragging forward - NOT add the tab to the group, its move it before the group
  ? consider adding blue line bellow tab when it is going to be group ?
  V when dragging tab onto group check if i can differentiate
    placing drop indicator after tab group - to add/move tab to first place in group
    placing drop indicator before tab group - to drag the tab to index before the group
  V when dragging tab-group-label dont show drop indicator inside another group
    show disallow drop indicator or place it before or after the group
  V check when to use elementIndex, both tab and tab-group-label have elementIndex getter/setter since Firefox 137
--------------------------------------------------------------------------------------------------
2025-03-20
  Bug 1950904 - [tabgroups] When dragging a tab from another window the insertion point indication is wrong
   V gBrowser.moveTabTo arguments changed
   V new private functions
     gBrowser._insertTabAtIndex -> gBrowser.#insertTabAtElementIndex
     gBrowser.tabContainer._getDropIndex -> gBrowser.tabContainer.#getDropIndex
     gBrowser.tabContainer._getDragTargetTab -> gBrowser.tabContainer.#getDragTarget
     gBrowser.#elementIndexToTabIndex
     gBrowser.#tabIndexToElementIndex
   V functions with different name
     gBrowser.tabContainer._finishAnimateTabMove -> gBrowser.tabContainer.finishAnimateTabMove


  Bug 1954275 - Expand group when dragging a link over collapsed group label
  V new private functions
    gBrowser.tabContainer._dragTime -> gBrowser.tabContainer.#dragTime
  - need to scroll if the opened tab are ih another row ???, what if the group is big
  V when dragging drop indicator vertical position is off when it is on group-label
  V when dragging drop indicator horizontal position it in not between the right tabs
    it is off by number of groups, when dragging to a position after groups

    1950904, 1954275

  - FIXME
    - need to add css style to keep tab-label and its tabs together or at least with the first tab
    V on link drop don't set drop indicator after selected tab
    V when dropping link before firstTabInRow there is a long delay before "tabmix-firstTabInRow" is set
    V when dragging link on group-label set show indicator before first tab in the group
    V when dropping link or tabs from other windows before group make sure the
      tabs are NOT added to the group
    V when dragging link on first tab in group and group-label is in previous row
      drop indicator is visible near the label even when the mouse is on the right side of the tab
    V when dropping link to last 25% of the last tab in a group should add the link to the group
    V when dropping link before group-label on first elementIndex
    - when dropping tab from another window to last 25% of the last tab in a group should add the link to the group
      this need to be handled by 1956067

    V when dragging pinned tab max index need to be pinnedtabcount-1 with dropbefore set to false
    V when dragging group the drop indicator after the group jump left and right when we move the tab to the right
    V when in multi-row mode with pinned tabs adding new group mess firstTabInRow setting also
      group-label position is not in the right row
    V when dragging tab between pinned and group-label, there is large gap after the tab
      scrolling fix it, check if it did not trigger setting first tab in rew
    V don't allow to move pinned tab into groups check handleDrop
    V can not drag tab out of last group to the end
    V when showing drop indicator after pinned tab before normal tab, show it before group-label if it is the next after last pinned tabs
    V when dropping link on group-label add the link as first tab in the group
    V Bug 1955361 - Dropping a link into a tab group opens a new tab outside of the group
      i have add a workaround until this but lands
    V can not drop before first item when it is a group in Firefox 136
    - test at 136 and 137
    - test RTL
    - consider adding blue line bellow tab when it is going to be group ?​
      check how firefox add/remove the color during drag
--------------------------------------------------------------------------------------------------
2025-04-03
 ChromeUtils.compileScript
 - TODO: try adding directive at the end to improve readability
 - try to add getter/setter in evaluateScripts
 - create all private methods in one place and resolve all before evaluating other
   function that depened on provate methods , or instead of passing promiseToWait
   just wait if the function contain any private methos, then call this.verifyPrivateMethodReplaced
   this.promiseToWait will be true if private methis exist in the code and not all private promises resolved
  - use defer promise in Tabmix.getPrivateMethod
  - we don't need promiseToWait at all, modify this.verifyPrivateMethodReplaced to show error only
    if there is no promise in Tabmix.privateMethodPromises for that private methos

  Cu.evalInSandbox
  V test using Cu.evalInSandbox
    https://devdoc.net/web/developer.mozilla.org/en-US/docs/Components.utils.evalInSandbox.html
  V try to pass file name and line number
--------------------------------------------------------------------------------------------------
2025-04-02
  - for issue https://github.com/onemen/TabMixPlus/issues/423
  - try to make sure `gBrowser.tabContainer._updateCloseButtons` are called
    after default home pages tabs are opened
  - check when Waterfox add "closebuttons"
  - make sure we call `gBrowser.tabContainer._updateCloseButtons` when we set the pref to 0
  - check when we set `Tabmix.tabsUtils.closeButtonsEnabled`
  - verify that handleResize does not use the original function in the listener and mutation observer
  `
  let handleResize = () => {
    this._updateCloseButtons();
    this._handleTabSelect(true);
  };
  `
--------------------------------------------------------------------------------------------------
2025-04-20
Tab group not collapsing: https://github.com/onemen/TabMixPlus/issues/432
- can not collapsed gropup if "when closing tab, select" option is set to "last selected tab"
  and last selected tab is in the group
- can not collapsed gropup, when all the tabs are in a group,
  and the option "Open other tabs next to current one" is on (browser.tabs.insertAfterCurrent)
--------------------------------------------------------------------------------------------------
2025-04-20
  - adding first pinned tab to multi-row does not set firstTabInRow properly
    when it is scrolled to the top
--------------------------------------------------------------------------------------------------
2025-04-25
  - update tabmix-build to add new entry to release-dates
  - update tabmix-build to add new comment at the top of the changelog about supported versions
--------------------------------------------------------------------------------------------------
2025-04-23
  make the right-click menu items for tabs revert to Firefox's default sorting
  issues/430:   https://github.com/onemen/TabMixPlus/issues/430
  V maybe it is simpler to keep all boolean preference + list of ids go generate checkboxes
  X if we keep TabContextConfig.sys.mjs rearrange it to only export what we need
    consider move all code form tab.js to it
  V need to simplify the code
    currently if we read the DOM, we can get elements from other extensions
    maybe it is best to create JSON file with the data of ids, key, old pref...
    and generate the checkboxes from it in menu pane
    and use it in Preferences utils to migrate the old preferences
    i will update the code if TabContextMenu.updateContextMenu changed

  V add options for new items in the menu
    unload tab / tabs
    mute tab / tabs
    bookmarks tab / tabs
    close all tabs
    close similar tabs
    add tab to new group / add tab to group
    remove tab from group
  V find a way to get all build-in menu items and dynamic items
  V when only one tab exist, there are some menuseparator appears one after the other
  V design new preference system for the menu
  - design editor to let the user to arrange menu items
  - currently we don't have option to hide some items in submenus
  V firefox add Share (class="share-tab-url-item") menu item only on first popupshowing
    some other menu may also added this way, need to findout how to add those to menu options
    to let user options to hide these items (trigger popupshowing event?)
    need to find a way to get the order of this item
  V consider moveing close-menu-list in firefox order after other closeing items
  V some ids that are used in clicks to show/hide menu items are not exist
    for example context_toggleMuteTabs "context_bookmarkTabs"
  V add check when loading the context menu for the first time that all items have id
    keep list for known items without ids
  - update code in updateTabContextMenu to handle new items
  V update function that handle menuseparator, hide menuseparator when it is the last item
  - add show all/hide all?
--------------------------------------------------------------------------------------------------
2025-07-19
  V fix support version in readme and doc from 128
  V drop old code before firefox 128
  V drop deprecated types used before firefox 128
  V add note in the readme about payment
  V check for new license
  V add notification about firefox-script update
