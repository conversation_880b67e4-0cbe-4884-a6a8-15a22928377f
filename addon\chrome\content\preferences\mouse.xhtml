<?xml version="1.0"?>

<!DOCTYPE overlay [
<!ENTITY % pref-tabmixDTD SYSTEM "chrome://tabmixplus/locale/pref-tabmix.dtd">
%pref-tabmixDTD;
]>

<overlay id="LinksPaneOverlay"
         xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
         xmlns:html="http://www.w3.org/1999/xhtml">

  <prefpane id="paneMouse" onpaneload="gMousePane.init();">
    <!-- scripts -->
    <script type="application/javascript" src="chrome://tabmixplus/content/preferences/mouse.js"/>

    <!-- preferences - list all preferences in this pane -->
    <preferences>
      <preference id="pref_mouse"             name="extensions.tabmix.mouse.selectedTabIndex"
                  type="int"/>
      <preference id="pref_tabclick"          name="extensions.tabmix.tabclick.selectedTabIndex"
                  type="int"/>
      <preference id="pref_mouseHoverSelect"  name="extensions.tabmix.mouseOverSelect"    type="bool"/>
      <preference id="pref_mouseOverSelectDelay"
                  name="extensions.tabmix.mouseOverSelectDelay" type="int"/>
      <preference id="pref_tabFlip"           name="extensions.tabmix.tabFlip"            type="bool"/>
      <preference id="pref_tabFlipDelay"      name="extensions.tabmix.tabFlipDelay"       type="int"/>
      <preference id="pref_tabbarscrolling"   name="extensions.tabmix.scrollTabs"         type="int"
                  notChecked="2"/>
      <preference id="pref_reversescrolling"  name="extensions.tabmix.reversedScroll"     type="bool"/>
      <preference id="pref_useScrollByTabs"    name="extensions.tabmix.useScrollByTabs"     type="bool"/>
      <preference id="pref_scrollByTabs"       name="extensions.tabmix.scrollByTabs"        type="int"/>
      <preference id="pref_mouseDownSelect"   name="extensions.tabmix.selectTabOnMouseDown"
                  type="bool" inverted="true"/>
      <preference id="pref_middleclickDelete" name="extensions.tabmix.middleclickDelete"  type="bool"/>
      <preference id="pref_lockTabSizingOnClose" name="extensions.tabmix.lockTabSizingOnClose"  type="bool"/>
      <preference id="pref_moveTabOnDragging" name="extensions.tabmix.moveTabOnDragging"  type="bool"/>
      <preference id="pref_dblClickTab"       name="extensions.tabmix.dblClickTab"        type="int"/>
      <preference id="pref_dblClickTabbar"    name="extensions.tabmix.dblClickTabbar"     type="int"/>
      <preference id="pref_middleClickTab"    name="extensions.tabmix.middleClickTab"     type="int"/>
      <preference id="pref_middleClickTabbar" name="extensions.tabmix.middleClickTabbar"  type="int"/>
      <preference id="pref_ctrlClickTab"      name="extensions.tabmix.ctrlClickTab"       type="int"/>
      <preference id="pref_ctrlClickTabbar"   name="extensions.tabmix.ctrlClickTabbar"    type="int"/>
      <preference id="pref_shiftClickTab"     name="extensions.tabmix.shiftClickTab"      type="int"/>
      <preference id="pref_shiftClickTabbar"  name="extensions.tabmix.shiftClickTabbar"   type="int"/>
      <preference id="pref_altClickTab"       name="extensions.tabmix.altClickTab"        type="int"/>
      <preference id="pref_altClickTabbar"    name="extensions.tabmix.altClickTabbar"     type="int"/>
      <preference id="pref_click_dragwindow"       name="extensions.tabmix.tabbar.click_dragwindow"
                  type="bool" inverted="true"
                  onchange="gMousePane.updateDblClickTabbar(this);"/>
      <preference id="pref_dblclick_changesize"    name="extensions.tabmix.tabbar.dblclick_changesize"
                  type="bool" inverted="true"/>
    </preferences>

    <!-- pane content -->
    <tabbox
            onselect="gPrefWindow.tabSelectionChanged(event);">
      <tabs id="mouse">
        <tab label="&mouseGesture.label;" class="subtabs" helpTopic="Mouse_-_Mouse_Gestures"/>
        <tab label="&mouseClick.label;" class="subtabs" helpTopic="Mouse_-_Mouse_Clicking"/>
      </tabs>
      <tabpanels>
        <tabpanel>
          <html:fieldset flex="1">
            <hbox align="center">
              <checkbox_tmp id="mouseHoverSelect" label="&mouseHoverSelect.labelBegin;"
                        preference="pref_mouseHoverSelect"/>
              <html:input id="mouseOverSelectDelay" size="4" maxlength="4"
                        preference="pref_mouseOverSelectDelay" type="number" required="required" observes="obs_mouseHoverSelect" min="0"/>
              <label value="&milliseconds.label;" observes="obs_mouseHoverSelect" class="timelabel"/>
            </hbox>
            <checkbox_tmp id="tabFlip" label="&tabFlip.label;" preference="pref_tabFlip"/>
            <hbox align="center" class="indent">
              <label value="&tabFlip.delay;" observes="obs_tabFlip"/>
              <html:input id="tabFlipDelay" size="4" maxlength="4" preference="pref_tabFlipDelay"
                        type="number" required="required" observes="obs_tabFlip" min="0"/>
              <label value="&milliseconds.label;" observes="obs_tabFlip" class="timelabel"/>
            </hbox>
            <!-- Drag tabs -->
            <checkbox_tmp id="mouseDownSelect" label="&clickFocus.label;" preference="pref_mouseDownSelect"/>
            <checkbox_tmp id="middleclickDelete" label="&removeEntries.label;"
                      tooltiptext="&removeEntries.tooltip;" preference="pref_middleclickDelete"/>
            <checkbox_tmp id="lockTabSizingOnClose" label="&lockTabSizingOnClose.label;"
                      preference="pref_lockTabSizingOnClose"/>
            <!-- drop indicator -->
            <checkbox_tmp id="moveTabOnDragging" label="&moveTabOnDragging.label;" preference="pref_moveTabOnDragging"
                          style="width: 470px;"/>
          </html:fieldset>
          <!-- Tabbar Scrolling -->
          <html:fieldset flex="1">
            <html:legend>
               <checkbox_tmp id="enableTabsScroll" label="&tabbarscrolling.caption;"
                             preference="pref_tabbarscrolling"
                             control="tabbarscrolling"
                             onsyncfrompreference="return gPrefWindow.syncfrompreference(this);"
                             onsynctopreference="return gPrefWindow.synctopreference(this, 1);"/>
            </html:legend>
            <separator class="thin"/>
            <label value="&tabbarscrolling.holdShift.label;" observes="obs_tabbarscrolling"/>
            <radiogroup id="tabbarscrolling" orient="horizontal"
                        preference="pref_tabbarscrolling">
              <radio value="1" id="tabbarscrolling-select" label="&tabbarscrolling.selectTab.label;"
                     observes="obs_tabbarscrolling"/>
              <radio value="0" id="tabbarscrolling-default" label="&tabbarscrolling.scrollAllTabs.label;"
                     observes="obs_tabbarscrolling"/>
            </radiogroup>
            <checkbox_tmp id="reversescrolling" label="&tabbarscrolling.inverse.label;"
                      preference="pref_reversescrolling" observes="obs_tabbarscrolling"/>
            <separator class="thin"/>
            <hbox align="center">
              <checkbox_tmp label="Enable `Scroll by Tabs`" id="useScrollByTabs" preference="pref_useScrollByTabs" observes="obs_tabbarscrolling"/>
              <label value="Scroll by" observes="obs_useScrollByTabs"/>
              <html:input id="tabFlipDelay" size="2" maxlength="2" preference="pref_scrollByTabs" observes="obs_useScrollByTabs"
                          type="number" required="required" min="1" max="6"
                          onsyncfrompreference="this.nextSibling.value=this.value==='1'?'tab':'tabs'"/>
              <label value="tab(s)" observes="obs_useScrollByTabs"/>
            </hbox>
          </html:fieldset>
        </tabpanel>
        <tabpanel class="mouseclick-panel" align="start">
          <label value="&clicktab.label;" class="bold-label"/>
          <spacer style="height: 1em;"/>
          <tabbox id="mouseclick_tabbox" class="groupbox-tabbox" flex="0"
                  onselect="gMousePane.tabSelectionChanged(event);">
            <tabs id="tabclick">
              <tab label="&double.label;" linkedpanel="pl" class="subtabs"/>
              <tab label="&middle.label;" linkedpanel="pl" class="subtabs"/>
              <tab label="&ctrl.label;"   linkedpanel="pl" id="tabId" label2="&cmd.label;" class="subtabs"/>
              <tab label="&shift.label;"  linkedpanel="pl" class="subtabs"/>
              <tab label="&alt.label;"    linkedpanel="pl" class="subtabs"/>
            </tabs>
            <tabpanels class="groupbox-panels tabclick" flex="0" selectedIndex="0"
                  onselect="gMousePane.panelSelectionChanged(event);">
              <tabpanel id="_tabpanel">
                <spacer style="height: 8px;"/>
                <!-- click on a tab -->
                <checkbox_tmp label="&ontab.label;" control="ClickTab" align="center" flex="1" oncommand="gMousePane.resetPreference(this);"/>
                <menulist id="ClickTab" class="indent" onsyncfrompreference="gMousePane.setCheckedState(this);" sizetopopup="pref">
                  <menupopup class="clicktab-popup" onpopupshown="gMousePane.ensureElementIsVisible(this);">
                    <menuitem value="-1" label="&clicktab.default;" hidden="true"/>
                    <menuitem value="0" label="&clicktab.nothing;"/>
                      <menuseparator/>
                    <menuitem value="33" label="Multi-select" class="multiselectitem"/>
                    <menuitem value="34" label="Ranged-select" class="multiselectitem"/>
                      <menuseparator class="multiselectitem"/>
                    <menuitem value="1" label="&clicktab.addtab;"/>
                    <menuitem value="3" label="&clicktab.duplicatetab;"/>
                    <menuitem value="14" label="&clicktab.duplicatetabw;" observes="obs_singleWindow"/>
                    <menuitem value="27" label="&clicktab.detachtab;" observes="obs_singleWindow"/>
                    <menuitem value="31" id="ClickTabPinTab"/>
                      <menuseparator/>
                    <menuitem value="5" label="&clicktab.protecttab;"/>
                    <menuitem value="6" label="&clicktab.locktab;"/>
                    <menuitem value="15" label="&clicktab.freezetab;"/>
                    <menuitem value="11" label="&clicktab.renametab;"/>
                    <menuitem value="28" label="&clicktab.copyTabUrl;"/>
                    <menuitem value="29" label="&clicktab.copyUrlFromClipboard;"/>
                    <menuitem value="22" label="&clicktab.selectMerge;"/>
                    <menuitem value="23" label="&clicktab.mergeTabs;"/>
                    <menuitem value="25" label="&clicktab.bookTab;"/>
                    <menuitem value="26" label="&clicktab.bookTabs;"/>
                      <menuseparator/>
                    <menuitem value="4" label="&clicktab.reloadtab;"/>
                    <menuitem value="7" label="&clicktab.reloadtabs;"/>
                    <menuitem value="16" label="&clicktab.reloadothertabs;"/>
                    <menuitem value="19" label="&clicktab.reloadlefttabs;"/>
                    <menuitem value="20" label="&clicktab.reloadrighttabs;"/>
                    <menuitem value="30" label="&clicktab.autoReloadTab;"/>
                      <menuseparator/>
                    <menuitem value="2" data-lazy-l10n-id="tab-context-close-n-tabs" data-l10n-args='{"tabCount": 1}'/>
                    <menuitem value="9" label="&clicktab.removeall;"/>
                    <menuitem value="24" label="&clicktab.removesimilar;"/>
                    <menuitem value="8" label="&clicktab.removeother;"/>
                    <menuitem value="17" label="&clicktab.removetoLeft;"/>
                    <menuitem value="18" label="&clicktab.removetoRight;"/>
                      <menuseparator/>
                    <menuitem value="10" label="&clicktab.uctab;" observes="obs_undoClose"/>
                    <menuitem value="13" label="&clicktab.ucatab;" observes="obs_undoClose"/>
                    <menuitem value="12" label="&clicktab.snapback;"/>
                    <menuitem value="21" label="&clicktab.ietab;"/>
                  </menupopup>
                </menulist>
                <spacer style="height: 16px;"/>
                <!-- click on a tabbar -->
                <checkbox_tmp label="&ontabbar.label;" control="ClickTabbar" oncommand="gMousePane.resetPreference(this);"/>
                <!-- we will clone the menupopup from ClickTab on startup -->
                <menulist id="ClickTabbar" class="indent" onsyncfrompreference="gMousePane.setCheckedState(this);" sizetopopup="pref"/>
                <!-- place holder to set tabbox width -->
                <checkbox_tmp label="&ontabbar.dblClick.label;" style="visibility: hidden;"/>
              </tabpanel>
              <tabpanel id="pl"></tabpanel>
            </tabpanels>
          </tabbox>
          <spacer style="height: 12px;"/>
          <checkbox_tmp id="click_dragwindow" label="&ontabbar.click.label;"
                        preference="pref_click_dragwindow"/>
          <checkbox_tmp id="dblclick_changesize" label="&ontabbar.dblClick.label;"
                        preference="pref_dblclick_changesize"/>
        </tabpanel>
      </tabpanels>
    </tabbox>

    <broadcasterset id="paneMouse:Broadcaster">
      <broadcaster id="obs_mouseHoverSelect"/>
      <broadcaster id="obs_tabFlip"/>
      <broadcaster id="obs_tabbarscrolling"/>
      <broadcaster id="obs_useScrollByTabs"/>
    </broadcasterset>

  </prefpane>
</overlay>
