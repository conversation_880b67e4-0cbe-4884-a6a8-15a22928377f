/**
    Firefox version 119+

    All platform

**/

/********** global styles for pref-appearance.css ********/

@import url("chrome://global/skin/in-content/common.css");
@import url("chrome://tabmixplus/skin/app_version/all/preferences-proton.css");
@import url("chrome://tabmixplus/skin/app_version/all/numberinput.css");

@namespace html url("http://www.w3.org/1999/xhtml");

checkbox[anonid]:-moz-focusring > .checkbox-check {
  outline: none;
}

.reset-button-box button {
  margin-inline-start: -2px;
}

.dialog-button[dlgtype="extra2"] {
  background-color: transparent;
  margin-inline-start: 0;
}
