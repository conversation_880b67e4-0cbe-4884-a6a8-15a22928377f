<?xml version="1.0"?>

<!DOCTYPE overlay [
<!ENTITY % pref-tabmixDTD SYSTEM "chrome://tabmixplus/locale/pref-tabmix.dtd">
%pref-tabmixDTD;
]>

<overlay id="IncompatiblePaneOverlay"
         xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul">

  <prefpane id="paneIncompatible" onpaneload="gIncompatiblePane.init();" helpTopic="introduction">

    <tabbox>
      <tabs class="tabs-hidden">
        <tab label="tab"/>
        <tab label="tab"/>
      </tabs>
      <tabpanels flex="1">
        <groupbox align="stretch" flex="1">
          <hbox align="center">
            <description style="width: 24em;" class="font-bold">
              &incompatible.extensions;
            </description>
            <spacer flex="1"/>
            <button class="font-bold" label="&incompatible.button.label;…"
                    oncommand="gIncompatiblePane.checkForIncompatible(true);"/>
          </hbox >
        </groupbox>
      </tabpanels>
    </tabbox>

  </prefpane>

</overlay>
