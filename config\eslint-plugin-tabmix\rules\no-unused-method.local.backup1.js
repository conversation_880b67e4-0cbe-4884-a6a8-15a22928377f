/*

look for unused methods

TODO:
look for unused assignments

*/

export default {
  meta: {
    messages: {unused: "Method '{{name}}' is unused."},
    type: "problem",
  },
  create(context) {
    // let globalScope, parents;

    const ignoreList = [
      "toCode",
      "catch",
      "console",
      "getElementById",
      "document",
      "stopPropagation",
      "preventDefault",
    ];

    const unUsedMethods = new Set();
    const usedMethods = new Set();
    const currentFile = context.getFilename();
    console.log("Current file being processed:", currentFile);

    /*
case:1
Tabmix.__test = function() {
  console.log("in Tabmix.__test: this === Tabmix", this === Tabmix);
}

case:2
// this is not Tabmix
Tabmix.__test = () => {
  console.log("in Tabmix.__test: this === Tabmix", this === Tabmix);
}

case 3
var navToolbox = {
}

in case1 this is Tabmix inside test function
in case2 this is not Tabmix this belong to the scope that the code is in
in case3 this Tabmix.navToolbox

*/

    // TODO: fix this...
    function getIdentifierForThis(parent) {
      // let parent = node.parent;
      // let parent = node;
      while (parent && parent.type !== "Program") {
        // if (parent.type === 'property') {
        //   console.log("parent.parent.id.name", parent.parent.id.name);
        // }
        // console.log("this", parent);
        // parent = parent.parent;
        // const {id, property: _property, type: _type, key} = parent;
        // const maybeIdentifier = Object.entries(parent).find(([k, v]) => {
        //   // if (v?.type === "Identifier") {
        //   //   console.log(k, v);
        //   // }
        //   // console.log(k);
        //   return v?.type === "Identifier";
        // });
        // console.log(maybeIdentifier);

        if (parent.type === "AssignmentExpression") {
          console.log("++++++++++++++++++++++++++++++++++++++");

          const list = getIdentifierNames(parent.left, parent.type);
          console.log("AssignmentExpression left ids", list.join("."), parent.left.right?.type);
        }
        parent = parent.parent;

        // console.log("parent", _type, id?.name ?? _property?.name ?? key?.name, Object.keys(parent));
        // console.log("parent", _type, maybeIdentifier?.name, id?.name ?? _property?.name ?? key?.name, Object.keys(parent));
      }
    }

    function getIdentifierNames(next, type) {
      const list = [];
      while (next?.type === "MemberExpression") {
        const {object, property} = next;
        if (property.type === "Identifier") {
          if (type === "CallExpression" && ignoreList.includes(property.name)) {
            return [];
          }
          list.unshift(property.name);
        }
        // TODO: move these 2 block after the while loop
        if (!object) {
          list.unshift(next.name);
          break;
        }
        if (object.type === "Identifier") {
          list.unshift(object.name);
          break;
        } else if (object.type === "ThisExpression") {
          // getIdentifierForThis(object);
          list.unshift("this");
          break;
        } else {
          next = type === "CallExpression" ? (object.callee ?? object) : object;
        }
      }
      return list;
    }

    function searchInProperties(name, properties) {
      // console.log(name, properties.length);
      const typeList = ["CallExpression", "FunctionExpression"];
      for (const property of properties) {
        // ["CallExpression", "FunctionExpression"]
        //
        if (typeList.includes(property.value?.type)) {
          console.log("  +", name, property.key.name, property.value?.type);
          // unUsedMethods.push(`${name}.${property.key.name}`);
          unUsedMethods.add(`${name}.${property.key.name}`);
        } else {
          // console.log("  -", property.kind, property.method, name, property.key.name, property.value?.type);
        }
        // const kind = property.method ? "method" : property.kind;
        // if (property.key) {
        //   console.log("  ", kind, property.method, name, property.key.name);
        // } else {
        //   console.log(property);
        // }
      }
    }

    let currentObjectName = "";

    return {
      "ExpressionStatement": function (node) {
        // console.log("ExpressionStatement starts");
        const type = node.expression?.type;
        // console.log(node.expression.left);
        if (type !== "AssignmentExpression" && type !== "CallExpression") {
          return;
        }
        // if (node.expression?.type === "AssignmentExpression") {
        // let next = node.expression.left;
        // const {left, right: {type: rightType}} = node.expression;
        // if (rightType === "FunctionExpression" || rightType === "ArrowFunctionExpression") {
        const {callee, left, right} = node.expression;
        if (type === "AssignmentExpression") {
          const typeList = ["FunctionExpression", "ArrowFunctionExpression", "ObjectExpression"];
          // ObjectExpression
          if (!typeList.includes(right.type)) {
            return;
          }
        }
        // let next = type === "AssignmentExpression" ? left : callee;
        // const list = [];
        // // while (next && next.type === "MemberExpression") {
        // while (next?.type === "MemberExpression") {
        //   const {object, property} = next;
        //   if (property.type === "Identifier") {
        //     if (type === "CallExpression" && ignoreList.includes(property.name)) {
        //       return;
        //     }
        //     list.unshift(property.name);
        //   }
        //   // if (!object && !next.name) {
        //   //   console.log("--------------------------------");
        //   //   console.log(next);
        //   //   console.log("--------------------------------");
        //   //   break;
        //   // }

        //   // TODO: move these 2 block after the while loop
        //   if (!object) {
        //     list.unshift(next.name);
        //     break;
        //   }
        //   if (object.type === "Identifier") {
        //     list.unshift(object.name);
        //     break;
        //   } else if (object.type === "ThisExpression") {
        //     // let parent = node.parent;
        //     // let parent = node;
        //     // while (parent && parent.type !== "Program") {
        //     //   // if (parent.type === 'property') {
        //     //   //   console.log("parent.parent.id.name", parent.parent.id.name);
        //     //   // }
        //     //   // console.log("this", parent);
        //     //   parent = parent.parent;
        //     //   const {id, property: _property, type: _type, key} = parent;
        //     //   const maybeIdentifier = Object.entries(parent).find(([k, v]) => {
        //     //     // if (v?.type === "Identifier") {
        //     //     //   console.log(k, v);
        //     //     // }
        //     //     // console.log(k);
        //     //     return v?.type === "Identifier";
        //     //   });
        //     //   // console.log(maybeIdentifier);

        //     //   if (parent.type === "AssignmentExpression") {
        //     //     console.log(parent.left);
        //     //   }

        //     //   // console.log("parent", _type, id?.name ?? _property?.name ?? key?.name, Object.keys(parent));
        //     //   console.log("parent", _type, maybeIdentifier?.name, id?.name ?? _property?.name ?? key?.name, Object.keys(parent));
        //     // }

        //     // console.log(...parents);
        //     // console.log({...globalScope});
        //     // XXXXX();
        //     // TODO: need to find a way to get the name of this
        //     // list.unshift(currentObjectName ? `(${currentObjectName}) this` : "this");
        //     list.unshift("this");
        //     break;
        //   } else {
        //     next = type === "CallExpression" ? object.callee ?? object : object;
        //   }
        // }
        // TODO: check if we need this??
        // if (!list.length) {
        //   list.push(node.expression.left.name);
        // }
        // const list1 = getIdentifierNames(type === "AssignmentExpression" ? left : callee, type);
        // if (list1.join('.') !== list.join('.')) {
        //   console.log("somthing is wrong", list.join('.'), list1.join('.'));
        // }
        const list = getIdentifierNames(type === "AssignmentExpression" ? left : callee, type);

        if (list[0] === "this") {
          console.log("---------------------------------------------");
          console.log(type, right?.type, list.join("."));
          // getIdentifierForThis(node);
          getIdentifierForThis(type === "AssignmentExpression" ? left : callee);
        }

        if (type === "AssignmentExpression") {
          currentObjectName = list.join(".");
          if (right.type === "ObjectExpression") {
            searchInProperties(currentObjectName, right.properties);
          } else {
            console.log("  +", currentObjectName, right.type);
            // unUsedMethods.push(currentObjectName);
            unUsedMethods.add(currentObjectName);
          }
        } else if (list.length) {
          // currentObjectName = list.join('.');
          console.log("used method", list.join("."));
          // usedMethods.push(list.join('.'));
          usedMethods.add(list.join("."));
        }
      },

      "VariableDeclarator": function (node) {
        if (node.init?.type === "ObjectExpression") {
          searchInProperties(node.id.name, node.init.properties);
        }
      },

      "___ExpressionStatement:exit": function (node) {
        const type = node.expression?.type;
        if (type !== "CallExpression") {
          return;
        }
        let next = node.expression.callee;
        const list = [];
        // while (next && next.type === "MemberExpression") {
        while (next?.type === "MemberExpression") {
          const {object, property} = next;
          if (property.type === "Identifier") {
            if (ignoreList.includes(property.name)) {
              return;
            }
            // console.log(object.name, property.name);

            list.unshift(property.name);
          }
          // if (!object && !next.name) {
          //   console.log("--------------------------------");
          //   console.log(next);
          //   console.log("--------------------------------");
          //   break;
          // }

          // TODO: move these 2 block after the while loop
          if (!object) {
            list.unshift(next.name);
            break;
          }
          if (object.type === "Identifier") {
            list.unshift(object.name);
            break;
          } else if (object.type === "ThisExpression") {
            // TODO: need to find a way to get the name of this
            list.unshift(currentObjectName ? `(${currentObjectName}) this` : "this");
            break;
          } else {
            next = object.callee ?? object;
          }
        }
        // if (!list.length) {
        //   list.push(node.expression.left.name);
        // }
        currentObjectName = list.join(".");
        console.log("used method", currentObjectName);

        // // console.log(list.join('.'));
        // if (right.type === "ObjectExpression") {
        //   // console.log('ExpressionStatement');
        //   searchInProperties(currentObjectName, right.properties);
        // }
      },

      // Program(node) {
      //   parents = context.sourceCode.getAncestors(node);
      //   globalScope = context.sourceCode.getScope(node);
      // },

      "Program:exit": function () {
        console.log("Program:exit unUsedMethods:\n", unUsedMethods.size);
        console.log("Program:exit usedMethods:\n", usedMethods.size);

        for (const used of usedMethods) {
          if (unUsedMethods.has(used)) {
            unUsedMethods.delete(used);
          }
        }

        console.log("Program:exit unUsedMethods:\n", unUsedMethods);
      },
    };
  },
};
