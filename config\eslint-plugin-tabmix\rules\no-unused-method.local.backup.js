/*

look for unused methods

TODO:
look for unused assignments

*/

export default {
  meta: {
    messages: {unused: "Method '{{name}}' is unused."},
    type: "problem",
  },
  create(context) {
    const unUsedMethods = [];
    const currentFile = context.getFilename();
    console.log("Current file being processed:", currentFile);
    // let globalScope, parents;
    // console.log(context);
    // zzxz();

    function searchInProperties(name, properties) {
      // console.log(name, properties.length);
      const typeList = ["CallExpression", "FunctionExpression"];
      for (const property of properties) {
        // ["CallExpression", "FunctionExpression"]
        //
        if (typeList.includes(property.value?.type)) {
          console.log("  +", name, property.key.name, property.value?.type);
          unUsedMethods.push(`${name}.${property.key.name}`);
        } else {
          // console.log("  -", property.kind, property.method, name, property.key.name, property.value?.type);
        }
        // const kind = property.method ? "method" : property.kind;
        // if (property.key) {
        //   console.log("  ", kind, property.method, name, property.key.name);
        // } else {
        //   console.log(property);
        // }
      }
    }

    let currentObjectName = "";

    /*

    used method event.stopPropagation
    used method document.getElementById.hidePopup
    */

    const ignoreList = [
      "toCode",
      "catch",
      "getElementById",
      "document",
      "stopPropagation",
      "preventDefault",
    ];

    return {
      // Program(node) {
      // for (const item of node.body) {
      //   if (item.type === "VariableDeclaration") {
      //     // console.log(item.type);
      //     for (const declaration of item.declarations) {
      //       // console.log(declaration.type);
      //       if (declaration.type === "VariableDeclarator") {
      //         const name = declaration.id.name;
      //         if (name) {
      //           console.log(name);
      //         } else {
      //           // console.log(declaration);
      //         }
      //       }
      //     }
      //   }
      // }
      // },
      // VariableDeclaration(node) {
      //   for (const declaration of node.declarations) {
      //     if (declaration.type === "VariableDeclarator") {
      //       const name = declaration.id.name;
      //       // console.log("V-" + name);
      //       console.log(name, declaration.init?.type);
      //       if (declaration.init?.type === "ObjectExpression") {
      //         for (const property of declaration.init.properties) {
      //           const kind = property.method ? "method" : property.kind;
      //           if (property.key) {
      //             console.log("  ", kind, property.method, name, property.key.name);
      //           } else {
      //             console.log(property);
      //           }
      //         }
      //       }
      //     }
      //   }
      // },
      "ExpressionStatement": function (node) {
        // console.log("ExpressionStatement starts");
        const type = node.expression?.type;
        // console.log(node.expression.left);
        if (type !== "AssignmentExpression") {
          return;
        }
        // if (node.expression?.type === "AssignmentExpression") {
        // let next = node.expression.left;
        // const {left, right: {type: rightType}} = node.expression;
        // if (rightType === "FunctionExpression" || rightType === "ArrowFunctionExpression") {
        const {left, right} = node.expression;
        const typeList = ["FunctionExpression", "ArrowFunctionExpression", "ObjectExpression"];
        // ObjectExpression
        if (!typeList.includes(right.type)) {
          return;
        }
        let next = left;
        const list = [];
        // while (next && next.type === "MemberExpression") {
        while (next?.type === "MemberExpression") {
          const {object, property} = next;
          if (property.type === "Identifier") {
            list.unshift(property.name);
          }
          // if (!object && !next.name) {
          //   console.log("--------------------------------");
          //   console.log(next);
          //   console.log("--------------------------------");
          //   break;
          // }

          // TODO: move these 2 block after the while loop
          if (!object) {
            list.unshift(next.name);
            break;
          }
          if (object.type === "Identifier") {
            list.unshift(object.name);
            break;
          } else if (object.type === "ThisExpression") {
            // TODO: need to find a way to get the name of this
            list.unshift(currentObjectName ? `(${currentObjectName}) this` : "this");
            break;
          } else {
            next = object;
          }
        }
        // TODO: check if we need this??
        // if (!list.length) {
        //   list.push(node.expression.left.name);
        // }
        currentObjectName = list.join(".");
        // console.log(list.join('.'));
        if (right.type === "ObjectExpression") {
          // console.log('ExpressionStatement');
          searchInProperties(currentObjectName, right.properties);
        }
      },
      "VariableDeclarator": function (node) {
        if (node.init?.type === "ObjectExpression") {
          searchInProperties(node.id.name, node.init.properties);
        }
      },
      "ExpressionStatement:exit": function (node) {
        const type = node.expression?.type;
        if (type !== "CallExpression") {
          return;
        }
        let next = node.expression.callee;
        const list = [];
        // while (next && next.type === "MemberExpression") {
        while (next?.type === "MemberExpression") {
          const {object, property} = next;
          if (property.type === "Identifier") {
            if (ignoreList.includes(property.name)) {
              return;
            }
            // console.log(object.name, property.name);

            list.unshift(property.name);
          }
          // if (!object && !next.name) {
          //   console.log("--------------------------------");
          //   console.log(next);
          //   console.log("--------------------------------");
          //   break;
          // }

          // TODO: move these 2 block after the while loop
          if (!object) {
            list.unshift(next.name);
            break;
          }
          if (object.type === "Identifier") {
            list.unshift(object.name);
            break;
          } else if (object.type === "ThisExpression") {
            // TODO: need to find a way to get the name of this
            list.unshift(currentObjectName ? `(${currentObjectName}) this` : "this");
            break;
          } else {
            next = object.callee ?? object;
          }
        }
        // if (!list.length) {
        //   list.push(node.expression.left.name);
        // }
        currentObjectName = list.join(".");
        console.log("used method", currentObjectName);

        // // console.log(list.join('.'));
        // if (right.type === "ObjectExpression") {
        //   // console.log('ExpressionStatement');
        //   searchInProperties(currentObjectName, right.properties);
        // }
      },
      "Program:exit": function () {
        console.log("Program:exit", unUsedMethods);
      },
    };
  },
  // __create(context) {
  //   let usedMethods = new Set();
  //   function markMethodAsUsed(node) {
  //     console.log('markMethodAsUsed', node.key);
  //     if (node.key && node.key.name) {
  //       console.log('markMethodAsUsed', node.key?.name);
  //       usedMethods.add(node.key.name);
  //     }
  //   }

  //   function isMethodCalledWithinClass(node) {
  //     // console.log({
  //     //   'node.type': node.type,
  //     //   'node.callee.type': node.callee?.type,
  //     //   'node.callee.object.type': node.callee?.object?.type,
  //     // });
  //     return node.type === "CallExpression" && node.callee.type === "MemberExpression" && node.callee.object.type === "ThisExpression";
  //   }

  //   function isMethodCalledOutsideClass(node) {
  //     console.log({
  //       'node.type': node.type,
  //       'node.callee.type': node.callee?.type,
  //       'node.callee.object.type': node.callee?.object?.type,
  //       'node.callee.object.name': node.callee?.object?.name,
  //     });
  //     return node.type === "CallExpression" && node.callee.type === "MemberExpression" && node.callee.object.type === "Identifier" && node.callee.object.name === "someUtils";
  //   }

  //   return {
  //     ClassDeclaration(node) {
  //       console.log("ClassDeclaration", node);
  //       usedMethods = new Set(); // Reset used methods for each class
  //       node.body.body.forEach(classElement => {
  //         if (classElement.type === "MethodDefinition") {
  //           markMethodAsUsed(classElement.key);
  //         }
  //       });
  //     },
  //     CallExpression(node) {
  //       console.log("CallExpression", isMethodCalledWithinClass(node), isMethodCalledOutsideClass(node));
  //       if (isMethodCalledWithinClass(node) || isMethodCalledOutsideClass(node)) {
  //         markMethodAsUsed(node.callee.property);
  //       }
  //     },
  //     "Program:exit": function(node) {
  //       try {
  //         node.body.forEach(statement => {
  //           if (statement.type === "VariableDeclaration" && statement.declarations.length) {
  //             const declaration = statement.declarations[0];
  //             if (declaration.init && declaration.init.type === "ObjectExpression") {
  //               declaration.init.properties.forEach(property => {
  //                 if (property.value && property.value.type === "FunctionExpression") {
  //                   markMethodAsUsed(property.key);
  //                 }
  //               });
  //             }
  //           }
  //         });

  //         const className = context.sourceCode.getScope(node).block.id?.name;
  //         try {
  //           if (Array.isArray(node?.body?.body)) {
  //             node.body.body.forEach(classElement => {
  //               if (classElement.type === "MethodDefinition" && !usedMethods.has(classElement.key.name)) {
  //                 context.report({
  //                   node: classElement,
  //                   message: `Method '${classElement.key.name}' in class '${className}' is unused.`
  //                 });
  //               }
  //             });
  //           }
  //         } catch (error) {
  //           console.log(error);
  //         }
  //       } catch (error) {
  //         console.log(error);
  //       }
  //     }
  //   };
  // }
};
