TASK INSTRUCTIONS


modify updatePinnedTabsContainer such that when Tabmix.prefs.getBoolPref("pinnedTabScroll") and TabmixTabbar.isMultiRow  is true
we move all pinned tabs to gBrowser.tabContainer.arrowScrollbox and modify the internal functions of
appendChild, insertBefore, contains, prepend accordingly.

when Tabmix.prefs.getBoolPref("pinnedTabScroll") and TabmixTabbar.isMultiRow is false, move all pinned tabs to gBrowser.pinnedTabsContainer (id: "pinned-tabs-container")
and restore internal to the original

use _pinnedTabsContainer to hold the current container and only modify when need to

also check if the logic in positionPinnedTabs is right
