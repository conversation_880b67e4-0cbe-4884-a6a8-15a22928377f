<?xml version="1.0"?>

<?xml-stylesheet href="chrome://global/skin/" type="text/css"?>
<?xml-stylesheet href="chrome://tabmixplus/content/preferences/subdialogs/pref-appearance.css" type="text/css"?>
<?xml-stylesheet href="chrome://tabmixplus/skin/pref-appearance.css"?>

<!DOCTYPE window [
<!ENTITY % dialogDTD SYSTEM "chrome://tabmixplus/locale/pref-tabmix.dtd" >
%dialogDTD;
<!ENTITY % appearanceDTD SYSTEM "chrome://tabmixplus/locale/pref-appearance.dtd" >
%appearanceDTD;
]>

<window id="pref-TMP-styles"
        class="system-font-size"
        windowtype="mozilla:tabmixopt-appearance"
        xmlns="http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul"
        xmlns:html="http://www.w3.org/1999/xhtml"
        title="&styles.label;"
        persist="screenX screenY hide-RGB"
        onload="tabstyles.init();">
<dialog buttons="accept,cancel,extra1,extra2">
   <script>
     window.opener.document.documentElement.fixMozTabsForZen(document);
   </script>
   <script type="application/javascript" src="chrome://tabmixplus/content/preferences/subdialogs/pref-appearance-ce.js"/>
   <script type="application/javascript" src="chrome://tabmixplus/content/preferences/checkbox_tmp-ce.js"/>
   <script type="application/javascript" src="chrome://tabmixplus/content/utils.js"/>
   <script type="application/javascript" src="chrome://tabmixplus/content/preferences/numberinput.js"/>
   <script type="application/javascript" src="chrome://tabmixplus/content/preferences/subdialogs/pref-appearance.js"/>

   <tabbox id="AppearanceTabBox">
     <tabs id="stylestabs">
      <tab label="&currentTab.label;" id="_currentTab" />
      <tab label="&unloadedTabs.label;" id="_unloadedTab" />
      <tab label="&unreadTab.label;" id="_unreadTab" />
      <tab label="&otherTabs.label;" id="_otherTab" />
      <tab label="&progressMeter.label;" id="_progressMeter" />
     </tabs>
     <tabpanels flex="1" id="stylespanels">
       <tabstylepanel id="currentTab"/>
       <tabstylepanel id="unloadedTab"/>
       <tabstylepanel id="unreadTab"/>
       <tabstylepanel id="otherTab"/>
       <tabstylepanel id="progressMeter" _hidebox="true"/>
     </tabpanels>
   </tabbox>
   <label value="&hideRGB.label;" show="&showRGB.label;" hide="&hideRGB.label;" id="hide-RGB" persist="value" hidden="true"/>
   <hbox class="dialog-button-box" pack="end">
     <button dlgtype="extra2" class="dialog-button" hidden="true"/>
     <spacer anonid="spacer" flex="1"/>
     <button dlgtype="accept" class="dialog-button" icon="accept"/>
     <button dlgtype="cancel" class="dialog-button" icon="cancel"/>
     <button dlgtype="extra1" class="dialog-button" hidden="true" icon="help"/>
   </hbox>
</dialog>
</window>
