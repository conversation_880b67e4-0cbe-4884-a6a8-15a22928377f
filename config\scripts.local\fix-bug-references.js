#!/usr/bin/env node

import fs from 'fs';

// Read the changelog_modified.md file
const filePath = 'logs/changelog_modified.md';
let content = fs.readFileSync(filePath, 'utf8');

// Fix the double-linked bug references
content = content.replace(/\[\[bug (\d{5,7})\]\(https:\/\/bugzilla\.mozilla\.org\/show_bug\.cgi\?id=\d{5,7}\)\]\(https:\/\/bugzilla\.mozilla\.org\/show_bug\.cgi\?id=\d{5,7}\)/gi, 
                         "[bug $1](https://bugzilla.mozilla.org/show_bug.cgi?id=$1)");

// Write the fixed content back to the file
fs.writeFileSync(filePath, content);

console.log('Fixed bug references in', filePath);
