{
  "extends": "../../../config/base.tsconfig.json",
  "include": ["**/*.js", "**/*.sys.mjs"],
  "exclude": [
    "node_modules",
    "**/gecko/**/*.d.ts",
    "logs/**",
    "**/*~/*",
    "**/*~*.*",
    "**/*.local.*",
    // files with different scopes
    "**/preferences",
    "**/overlay",
    "**/scripts"
  ],
  "compilerOptions": {
    "types": ["index.d.ts", "general.d.ts", "addon.d.ts"]
  }
}
