/**
*  Bug 1884792 - Remove chrome-only :-moz-lwtheme pseudo-class
*  since Firefox 126 use :root[lwtheme] or :root[lwtheme-image]
*
*/

/* set background based on rules for #navigator-toolbox
 from chrome\browser\content\browser\browser.css */
:root[lwtheme] #tabmix-bottom-toolbox > toolbox {
  background-image: var(--lwt-additional-images);
  background-repeat: var(--lwt-background-tiling);
  background-position: var(--lwt-background-alignment);
}

:root[lwtheme] #tabmix-bottom-toolbox {
  background-color: var(--tabmix-bottom-toolbox-color, var(--lwt-accent-color));
}

:root[lwtheme-image] #tabmix-bottom-toolbox > toolbox {
  background-image: var(--lwt-header-image), var(--lwt-additional-images);
  background-repeat: no-repeat, var(--lwt-background-tiling);
  background-position: right top, var(--lwt-background-alignment);
}

:root[lwtheme] #tabmix-bottom-toolbox > toolbox:-moz-window-inactive {
  background-color: var(--tabmix-bottom-toolbox-color, var(--lwt-accent-color-inactive, var(--lwt-accent-color)));
}

@media (-moz-platform: windows) {
  :root:not([lwtheme]) #TabsToolbar[tabbaronbottom] {
    appearance: none;
    border-top: 1px solid rgb(10 31 51 / 35%);
  }
}

@media (-moz-platform: linux) {
  :root[lwtheme-brighttext] #tabmix-tabs-closebutton:not(:hover) {
    background-image: -moz-image-rect(url("chrome://global/skin/icons/close.svg"), 0, 80, 16, 64);
  }

  :root[lwtheme]:not([lwtheme-brighttext]) #tabmix-tabs-closebutton:not(:hover) {
    background-image: -moz-image-rect(url("chrome://global/skin/icons/close.svg"), 0, 96, 16, 80);
  }
}

@media (-moz-platform: macos) {
  /* for mac - look in pinstripe/browser/browser.css */
  :root[lwtheme] #tabbrowser-tabs #tabbrowser-arrowscrollbox .tabbrowser-tab:not(:hover) > * > * > .tab-label:not([selected="true"]),
  :root[lwtheme] #tabbrowser-tabs #tabbrowser-arrowscrollbox .tabbrowser-tab:not(:hover) > * > * > .tab-label-container > .tab-label:not([selected="true"]) {
    opacity: .8;
  }
}
