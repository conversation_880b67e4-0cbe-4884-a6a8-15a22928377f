#!/usr/bin/env node

import fs from "fs";
import { execSync } from "child_process";

// Check if a file path is provided as an argument
const inputFile = process.argv[2] || "logs/changelog_draft.md";
const outputFile = process.argv[3] || "logs/changelog_modified.md";

// Ensure the input file exists
if (!fs.existsSync(inputFile)) {
  console.error(`Error: Input file '${inputFile}' does not exist.`);
  process.exit(1);
}

// Read the input file
let content = fs.readFileSync(inputFile, "utf8");

// Apply the dev-Build to dev-build replacement
content = content.replace(/dev-Build/g, "dev-build");

// Get the notes from the original file (lines 1-3)
const lines = content.split("\n");
const notes = lines.slice(0, 3).join("\n");

// Process the rest of the content (from line 4 onwards)
const contentLines = lines.slice(3).join("\n").replace("### :wrench: Chores", "### :wrench: Maintenance").split("\n");

// Process lines based on their type
let mainContentLines = [];
let footerLines = [];
let processingFooter = false;

// First pass: categorize lines
for (const line of contentLines) {
  // Footer lines start with [
  if (line.match(/^\[.*\]:/)) {
    processingFooter = true;
  }
  
  if (processingFooter) {
    footerLines.push(line);
  } else {
    mainContentLines.push(line);
  }
}

// Process content lines
mainContentLines = mainContentLines.map(line => {
  // Only process commit lines (starting with -)
  if (line.startsWith('- ')) {
    return line.replace(/ \*\(commit by \[@[\w-]+\]\(https:\/\/github\.com\/[\w-]+\)\)\*/g, "")
      .replace(/PR (\[#\d+\].*)?\s?by \[@onemen\].*?\)/g, "$1")
      .replace(/-\s*(\[.{9}\][^)]*\))\s-\s(.*)/g, "- $2 ($1)")
      .replace(/__/g, "\\_\\_")
      .replace(/followup/gi, "follow up")
      .replace(/(-\s*)(\w)/g, (_, p1, p2) => p1 + p2.toUpperCase())
      .replace(/(Bug|bug)\s(\d{5,7})/g, "[$&](https://bugzilla.mozilla.org/show_bug.cgi?id=$2)");
  }
  
  return line;
});

// Group entries with the same title
const titleMap = new Map();
const processedLines = [];

for (const line of mainContentLines) {
  // Only process commit lines (starting with -)
  if (line.startsWith("- ") && line.includes("](https://github.com/onemen/TabMixPlus/commit/")) {
    // Extract the title (everything before the first commit link)
    const titleMatch = line.match(/- (.+?)\s\(\[`[\w\d]+`\]/);
    if (titleMatch) {
      const title = titleMatch[1];
      
      if (titleMap.has(title)) {
        // Extract commit hash and URL from current line
        const commitMatch = line.match(/\(\[`([\w\d]+)`\]\(https:\/\/github\.com\/onemen\/TabMixPlus\/commit\/([\w\d]+)\)\)/);
        if (commitMatch) {
          const existingLine = titleMap.get(title);
          const existingLineIndex = processedLines.indexOf(existingLine);
          
          // Create the new link string
          const linkStr = `[\`${commitMatch[1]}\`](https://github.com/onemen/TabMixPlus/commit/${commitMatch[2]})`;
          
          // Combine the lines by adding the new commit hash with proper formatting
          let updatedLine;
          if (existingLine.endsWith(')')) {
            // If the line already ends with a link, add a comma and the new link
            updatedLine = existingLine.replace(/\)$/,  `, ${linkStr})`);
          } else {
            // This shouldn't happen with the current format, but just in case
            updatedLine = `${existingLine} (${linkStr})`;
          }
          
          // Update the stored line
          processedLines[existingLineIndex] = updatedLine;
          titleMap.set(title, updatedLine);
          continue;
        }
      } else {
        titleMap.set(title, line);
      }
    }
  }
  
  processedLines.push(line);
}

// Combine all parts of the file with notes at the top
const result = [notes, ...processedLines, ...footerLines];

// Write the content to the output file
fs.writeFileSync(outputFile, result.join("\n"));

// Run the fix-bug-references.js script to fix any double-linked bug references
try {
  execSync(`node config/scripts.local/fix-bug-references.js`);
} catch (error) {
  console.error('Error running fix-bug-references.js:', error);
}

console.log(`Changelog modification complete. Output written to '${outputFile}'.`);
console.log(`Original content lines: ${mainContentLines.length}, Processed content lines: ${processedLines.length}`);

// Print a summary of merged lines if any were merged
const mergedCount = mainContentLines.length - processedLines.length;
if (mergedCount > 0) {
  console.log(`Merged ${mergedCount} duplicate entries.`);
}
