/* for zen browser */

#tabbrowser-tabs[orient="vertical"][closebuttons="noclose"] {
  & #tabbrowser-arrowscrollbox .tabbrowser-tab {
    & .tab-close-button {
      display: none !important;
    }
  }
}

#tabbrowser-tabs[orient="vertical"][closebuttons="alltabs"] {
  & #tabbrowser-arrowscrollbox .tabbrowser-tab:not(:is([pinned], [protected])) {
    & .tab-close-button {
      --tab-inline-padding: 0 !important;

      display: block !important;
      margin-inline-end: 0 !important;
    }
  }
}

#tabbrowser-tabs[orient="vertical"][closebuttons="activetab"] {
  & #tabbrowser-arrowscrollbox .tabbrowser-tab:not(:is([pinned], [protected]))[selected="true"] {
    & .tab-close-button {
      --tab-inline-padding: 0 !important;

      display: block !important;
      margin-inline-end: 0 !important;
    }
  }
}

#tabbrowser-tabs[orient="vertical"][closebuttons-hover="notactivetab"] {
  & #tabbrowser-arrowscrollbox .tabbrowser-tab:not(:is([pinned], [protected], [selected="true"]))[showbutton="on"] {
    & .tab-close-button {
      --tab-inline-padding: 0 !important;

      display: block !important;
      margin-inline-end: 0 !important;
    }
  }
}

#tabbrowser-tabs[orient="vertical"][closebuttons-hover="alltabs"] {
  & #tabbrowser-arrowscrollbox .tabbrowser-tab:not(:is([pinned], [protected]))[showbutton="on"] {
    & .tab-close-button {
      --tab-inline-padding: 0 !important;

      display: block !important;
      margin-inline-end: 0 !important;
    }
  }
}
