name: dev-build

on:
  push:
    branches: [ main ]

env:
  XPI_NAME: tab_mix_plus-dev-build
  PREVIOUS_TAG: ''
  NOTES: 'This release includes an XPI file that is automatically generated after each push to the main branch.<br />It compatible with all supported versions of Firefox. (see [Browser Compatibility](https://onemen.github.io/tabmixplus-docs/other/installation/#browser-compatibility))'

jobs:
  build-and-upload:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: main

      - name: Make scripts executable
        run: |
          chmod +x .github/scripts/*.js

      - name: Set version in install.rdf
        id: set_version
        run: |
          previous_tag=$(git tag --sort=-creatordate | grep -E '^v[0-9]+\.[0-9]+\.[0-9]+' | head -n 1)
          echo "PREVIOUS_TAG=$previous_tag" >> $GITHUB_ENV
          changelog_exist=$( [[ $(git rev-parse $previous_tag) != $(git rev-parse HEAD) ]] && echo true || echo false )
          echo "CHANGELOG_EXIST=$changelog_exist" >> $GITHUB_OUTPUT
          echo "CHANGELOG_EXIST=$changelog_exist" >> $GITHUB_ENV
          node ./.github/scripts/calculate_version.js "$previous_tag"

      - name: Zip addon folder
        run: |
          cd addon
          zip -rq /tmp/${{ env.XPI_NAME }}.xpi *

      - name: Update dev-build tag
        run: |
          if [ $(git rev-parse dev-build 2>/dev/null || echo "") != $(git rev-parse HEAD) ]; then
            git tag -d dev-build || true
            git tag dev-build HEAD
            git push origin -f dev-build
          fi

      - name: Create CHANGELOG
        if: ${{ steps.set_version.outputs.CHANGELOG_EXIST == 'true' }}
        uses: requarks/changelog-action@v1.10.2
        with:
          token: ${{ github.token }}
          fromTag: 'dev-build'
          toTag: ${{ env.PREVIOUS_TAG }}
          excludeTypes: ''

      - name: Process CHANGELOG
        if: ${{ steps.set_version.outputs.CHANGELOG_EXIST == 'true' }}
        run: node ./.github/scripts/process_changelog.js

      - name: Upload release asset to GitHub
        env:
          GH_TOKEN: ${{ github.token }}
        run: node ./.github/scripts/create_github_release.js

      - name: Update tabmixplus-docs
        env:
          GH_TOKEN: ${{ secrets.TABMIXPLUS_DOCS_TOKEN }}
        run: |
          gh workflow run --repo onemen/tabmixplus-docs --ref main deploy.yml
