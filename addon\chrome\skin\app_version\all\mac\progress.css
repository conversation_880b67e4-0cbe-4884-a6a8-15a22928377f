/**
    Firefox all versions

    Mac platform

**/

/* :::: progress bar on tabs :::: */
.tab-progress {
  appearance: none;
  min-height: 0;
  height: 4px;
}

#tabbrowser-tabs[tabsontop="true"] .tab-progress {
  margin: 3px 2px;
}

#tabbrowser-tabs:not([tabsontop="true"]) .tab-progress {
  margin: 1px 2px;
}

.tab-progress > .progress-bar {
  appearance: none;
  border: 0;
  border-top: 1px solid rgb(0 0 0 / 40%);
  border-bottom: 1px solid rgb(0 0 0 / 30%);
  background-origin: border-box;
  background-color: Highlight;
}

.tab-progress > .progress-bar:-moz-window-inactive {
  opacity: 0.6 !important;
}
